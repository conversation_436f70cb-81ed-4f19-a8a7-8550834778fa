import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';

import 'notification_constants.dart';

/// Notification channels configuration for awesome notifications
class NotificationChannels {
  /// Prayer notifications channel
  static NotificationChannel get prayerChannel => NotificationChannel(
        channelGroupKey: 'prayer_group',
        channelKey: 'prayer_channel',
        channelName: 'Prayer Notifications',
        channelDescription: 'Notifications for prayer times, athan, and iqamah',
        defaultColor: const Color(0xFF9D50DD),
        ledColor: const Color(0xFF9D50DD),
        importance: NotificationImportance.Max,
        channelShowBadge: true,
        onlyAlertOnce: false,
        playSound: true,
        // Removed hardcoded soundSource to allow custom sounds per notification
        criticalAlerts: true,
        enableVibration: true,
        enableLights: true,
        locked: true,
        defaultPrivacy: NotificationPrivacy.Public,
      );

  /// Athkar notifications channel
  static NotificationChannel get athkarChannel => NotificationChannel(
        channelGroupKey: 'athkar_group',
        channelKey: 'athkar_channel',
        channelName: 'Athkar Notifications',
        channelDescription:
            'Notifications for morning, evening athkar and dhikr reminders',
        defaultColor: const Color(0xFF4CAF50),
        ledColor: const Color(0xFF4CAF50),
        importance: NotificationImportance.High,
        channelShowBadge: true,
        onlyAlertOnce: false,
        playSound: true,
        soundSource: 'resource://raw/sou_tasbhamd', // Default dhikr sound
        criticalAlerts: false,
        // Removed defaultRingtoneType to allow custom sounds
        enableVibration: true,
        enableLights: true,
        defaultPrivacy: NotificationPrivacy.Public,
      );

  /// System notifications channel
  static NotificationChannel get systemChannel => NotificationChannel(
        channelGroupKey: 'system_group',
        channelKey: 'system_channel',
        channelName: 'System Notifications',
        channelDescription: 'System notifications and warnings',
        defaultColor: const Color(0xFFFF9800),
        ledColor: const Color(0xFFFF9800),
        importance: NotificationImportance.Default,
        channelShowBadge: true,
        onlyAlertOnce: true,
        playSound: true,
        criticalAlerts: false,
        // Removed defaultRingtoneType to allow custom sounds
        enableVibration: false,
        enableLights: true,
        defaultPrivacy: NotificationPrivacy.Public,
      );

  /// Firebase push notifications channel
  static NotificationChannel get firebaseChannel => NotificationChannel(
        channelGroupKey: 'firebase_group',
        channelKey: 'firebase_channel',
        channelName: 'Push Notifications',
        channelDescription: 'Remote push notifications from server',
        defaultColor: const Color(0xFF2196F3),
        ledColor: const Color(0xFF2196F3),
        importance: NotificationImportance.High,
        channelShowBadge: true,
        onlyAlertOnce: false,
        playSound: true,
        criticalAlerts: false,
        // Removed defaultRingtoneType to allow custom sounds
        enableVibration: true,
        enableLights: true,
        defaultPrivacy: NotificationPrivacy.Public,
      );

  /// Test notifications channel - specifically for testing custom sounds
  static NotificationChannel get testChannel => NotificationChannel(
        channelGroupKey: 'test_group',
        channelKey: 'test_channel',
        channelName: 'Test Notifications',
        channelDescription:
            'Channel for testing notifications with custom sounds',
        defaultColor: const Color(0xFFE91E63),
        ledColor: const Color(0xFFE91E63),
        importance: NotificationImportance.Max,
        channelShowBadge: true,
        onlyAlertOnce: false,
        playSound: true,
        soundSource:
            'resource://raw/athan3', // Set a default sound for the channel
        defaultRingtoneType: DefaultRingtoneType
            .Alarm, // Use alarm sound type for higher priority
        criticalAlerts: true,
        enableVibration: true,
        enableLights: true,
        locked: false,
        defaultPrivacy: NotificationPrivacy.Public,
      );

  /// Get all notification channels
  static List<NotificationChannel> get allChannels => [
        prayerChannel,
        athkarChannel,
        systemChannel,
        firebaseChannel,
        testChannel, // Add test channel to the list
      ];

  /// Get channel key by notification type
  static String getChannelKey(String notificationType) {
    switch (notificationType) {
      case NotificationConstants.prayerTime:
      case NotificationConstants.prePrayerWarning:
      case NotificationConstants.iqamahTime:
        return prayerChannel.channelKey!;

      case NotificationConstants.morningAthkar:
      case NotificationConstants.eveningAthkar:
      case NotificationConstants.dhikrReminder:
        return athkarChannel.channelKey!;

      default:
        return systemChannel.channelKey!;
    }
  }

  /// Get notification importance by type
  static NotificationImportance getImportance(String notificationType) {
    switch (notificationType) {
      case NotificationConstants.prayerTime:
      case NotificationConstants.iqamahTime:
        return NotificationImportance.Max;

      case NotificationConstants.prePrayerWarning:
      case NotificationConstants.morningAthkar:
      case NotificationConstants.eveningAthkar:
      case NotificationConstants.dhikrReminder:
        return NotificationImportance.High;

      default:
        return NotificationImportance.Default;
    }
  }
}
