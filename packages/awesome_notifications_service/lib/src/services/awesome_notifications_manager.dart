import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:get/route_manager.dart';
import 'package:logger/logger.dart';

import '../constants/notification_channels.dart';
import '../constants/notification_constants.dart';
import '../models/notification_data.dart' hide NotificationActionButton;
import '../utils/notification_utils.dart';
import 'permission_service.dart';

/// Core manager for awesome notifications
class AwesomeNotificationsManager {
  static final AwesomeNotificationsManager _instance =
      AwesomeNotificationsManager._internal();
  factory AwesomeNotificationsManager() => _instance;
  AwesomeNotificationsManager._internal();

  final Logger _logger = Logger();
  final PermissionService _permissionService = PermissionService();

  // Expose the raw awesome_notifications instance for direct access
  final AwesomeNotifications awesomeNotifications = AwesomeNotifications();

  bool _isInitialized = false;

  // Navigation callback for handling notification taps
  static Future<void> Function(ReceivedAction)? _navigationCallback;
  bool get isInitialized => _isInitialized;

  /// Set navigation callback for handling notification taps
  static void setNavigationCallback(
      Future<void> Function(ReceivedAction) callback) {
    _navigationCallback = callback;
  }

  /// Clear navigation callback
  static void clearNavigationCallback() {
    _navigationCallback = null;
  }

  /// Initialize awesome notifications with all channels
  Future<bool> initialize() async {
    try {
      _logger.d('Initializing AwesomeNotifications');

      // Configure timezone
      await NotificationUtils.configureLocalTimeZone();

      // Initialize awesome notifications
      final initialized = await awesomeNotifications.initialize(
        'resource://drawable/ic_launcher', // App icon
        NotificationChannels.allChannels,
        channelGroups: _createChannelGroups(),
        debug: true, // Set to true for debugging
        languageCode: 'ar', // Default language for notifications
      );

      if (!initialized) {
        _logger.e('Failed to initialize AwesomeNotifications');
        return false;
      }

      // Set up notification listeners
      _setupNotificationListeners();

      // Initialize permission service
      await _permissionService.initialize();

      _isInitialized = true;
      _logger.i('AwesomeNotifications initialized successfully');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error initializing AwesomeNotifications',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Request all required permissions
  Future<bool> requestPermissions() async {
    try {
      final result = await _permissionService.requestAllPermissions();
      return result.granted;
    } catch (e, stackTrace) {
      _logger.e('Error requesting permissions',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Check permission status
  Future<PermissionStatus> getPermissionStatus() async {
    return await _permissionService.checkPermissionStatus();
  }

  /// Schedule a notification
  Future<bool> scheduleNotification(NotificationData notificationData) async {
    _logger.d('fursan::  start scheduleNotification');
    try {
      if (!_isInitialized) {
        _logger.w('AwesomeNotifications not initialized');
        return false;
      }

      // Validate notification data
      if (!NotificationUtils.validateNotificationData(notificationData)) {
        _logger.w('Invalid notification data: $notificationData');
        return false;
      }

      // Check if time is in the future
      if (!NotificationUtils.isValidNotificationTime(
          notificationData.scheduledTime)) {
        _logger.w(
            'Notification time is in the past: ${notificationData.scheduledTime}');
        return false;
      }

      _logger.d('fursan:: start convert');

      // Convert to TZDateTime
      final scheduledDate = DateTime.parse(
          notificationData.scheduledTime.toString().replaceFirst("Z", ''));

      Get.log(
          'fursan:: scheduled Date : ${scheduledDate.toString()} notification id ${notificationData.id} - ${notificationData.title} (${notificationData.notificationType}) - ${notificationData.soundPath}');
      // Create notification content
      _logger.d(
          '🔊 Creating notification with sound path: ${notificationData.soundPath}');

      // Format sound path if provided
      final formattedSoundPath =
          NotificationUtils.formatSoundPath(notificationData.soundPath);
      _logger.d('Using formatted sound path: $formattedSoundPath');

      // Add more detailed logging for debugging
      _logger.d('📢 Notification sound details:');
      _logger.d('   - Original sound path: ${notificationData.soundPath}');
      _logger.d('   - Formatted sound path: $formattedSoundPath');
      _logger.d('   - Notification type: ${notificationData.notificationType}');
      _logger.d('   - Channel key: ${notificationData.channelKey}');

      // Create a map for additional data
      Map<String, String?> additionalData = {};
      if (notificationData.payload != null) {
        additionalData['payload'] = notificationData.payload;
      }

      // Add custom data to payload for proper cancellation logic
      if (notificationData.customData != null) {
        additionalData.addAll(notificationData.customData!);
      }

      // For athkar notifications, don't use customSound - rely on channel default
      String? customSoundToUse;
      if (notificationData.channelKey != 'athkar_channel') {
        // Only use custom sound for non-athkar notifications
        customSoundToUse = formattedSoundPath;
        _logger.d('🎵 Using custom sound: $customSoundToUse');
      } else {
        _logger.d('🎵 Using channel default sound for athkar notification');
      }

      // Add sound information for debugging
      if (formattedSoundPath != null) {
        additionalData['sound_path'] = formattedSoundPath;
      }

      final content = NotificationContent(
        id: notificationData.id,
        channelKey: notificationData.channelKey,
        title: notificationData.title,
        body: notificationData.body,
        payload: additionalData.isNotEmpty ? additionalData : null,
        notificationLayout: NotificationLayout.Default,
        wakeUpScreen: notificationData.wakeUpScreen,
        fullScreenIntent: notificationData.fullScreenIntent,
        criticalAlert: notificationData.criticalAlert,
        category: _getNotificationCategory(notificationData.notificationType),
        // Use custom sound only for non-athkar notifications
        customSound: customSoundToUse,

        icon: 'resource://drawable/ic_launcher',
        autoDismissible: true,
        showWhen: true,
        displayOnForeground: true,
        displayOnBackground: true,
        actionType: ActionType.Default,
      );

      // Create action buttons if provided
      List<NotificationActionButton>? actionButtons;
      if (notificationData.actionButtons != null &&
          notificationData.actionButtons!.isNotEmpty) {
        actionButtons = notificationData.actionButtons!
            .map((button) => NotificationActionButton(
                  key: button.key,
                  label: button.label,
                  requireInputText: false,
                  autoDismissible: button.autoDismissible,
                  isDangerousOption: button.isDangerousOption,
                ))
            .toList();
      }

      // Schedule the notification
      await AwesomeNotifications().createNotification(
        content: content,
        schedule: NotificationCalendar.fromDate(date: scheduledDate),
        actionButtons: actionButtons,
      );

      // Note: AwesomeNotifications.createNotification() often returns false even when successful
      // The actual success is indicated by the _onNotificationCreated callback
      // So we'll consider it successful if no exception was thrown
      _logger.d('Notification scheduling requested: ${notificationData.id}');

      return true; // Return true since no exception was thrown
    } catch (e, stackTrace) {
      _logger.e('Error scheduling notification',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Cancel a specific notification
  Future<bool> cancelNotification(int notificationId) async {
    try {
      if (!_isInitialized) {
        _logger.w('AwesomeNotifications not initialized');
        return false;
      }

      await AwesomeNotifications().cancel(notificationId);
      _logger.d('Notification cancelled: $notificationId');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error cancelling notification',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Cancel notifications by payload
  Future<bool> cancelNotificationsByPayload(String payload) async {
    try {
      if (!_isInitialized) {
        _logger.w('AwesomeNotifications not initialized');
        return false;
      }

      final scheduledNotifications =
          await AwesomeNotifications().listScheduledNotifications();
      int cancelledCount = 0;

      for (final notification in scheduledNotifications) {
        final notificationPayload = notification.content?.payload?['payload'];
        if (notificationPayload != null && notificationPayload == payload) {
          // Changed from contains() to exact equality
          await AwesomeNotifications().cancel(notification.content!.id!);
          cancelledCount++;
        }
      }

      _logger
          .d('Cancelled $cancelledCount notifications with payload: $payload');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error cancelling notifications by payload',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Cancel all notifications
  Future<bool> cancelAllNotifications() async {
    try {
      if (!_isInitialized) {
        _logger.w('AwesomeNotifications not initialized');
        return false;
      }

      await AwesomeNotifications().cancelAll();
      _logger.d('All notifications cancelled');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error cancelling all notifications',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  Future<bool> cancelAllSchedulesNotifications() async {
    try {
      if (!_isInitialized) {
        _logger.w('AwesomeNotifications not initialized');
        return false;
      }

      await AwesomeNotifications().cancelAllSchedules();
      _logger.d('All notifications cancelled');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error cancelling all notifications',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Get scheduled notifications
  Future<List<NotificationModel>> getScheduledNotifications() async {
    try {
      if (!_isInitialized) {
        _logger.w('AwesomeNotifications not initialized');
        return [];
      }

      return await AwesomeNotifications().listScheduledNotifications();
    } catch (e, stackTrace) {
      _logger.e('Error getting scheduled notifications',
          error: e, stackTrace: stackTrace);
      return [];
    }
  }

  /// Check if notification exists
  Future<bool> hasScheduledNotification(String payload) async {
    try {
      final notifications = await getScheduledNotifications();
      return notifications.any((notification) {
        final notificationPayload = notification.content?.payload?['payload'];
        return notificationPayload != null &&
            notificationPayload ==
                payload; // Changed from contains() to exact equality
      });
    } catch (e) {
      _logger.e('Error checking scheduled notification: $e');
      return false;
    }
  }

  /// Create channel groups
  List<NotificationChannelGroup> _createChannelGroups() {
    return [
      NotificationChannelGroup(
        channelGroupKey: 'prayer_group',
        channelGroupName: 'Prayer Notifications',
      ),
      NotificationChannelGroup(
        channelGroupKey: 'athkar_group',
        channelGroupName: 'Athkar Notifications',
      ),
      NotificationChannelGroup(
        channelGroupKey: 'system_group',
        channelGroupName: 'System Notifications',
      ),
      NotificationChannelGroup(
        channelGroupKey: 'firebase_group',
        channelGroupName: 'Push Notifications',
      ),
    ];
  }

  /// Get notification category based on type
  NotificationCategory _getNotificationCategory(String notificationType) {
    switch (notificationType) {
      case NotificationConstants.prayerTime:
      case NotificationConstants.iqamahTime:
        return NotificationCategory.Alarm;
      case NotificationConstants.prePrayerWarning:
        return NotificationCategory.Reminder;
      case NotificationConstants.morningAthkar:
      case NotificationConstants.eveningAthkar:
      case NotificationConstants.dhikrReminder:
        return NotificationCategory.Reminder;
      default:
        return NotificationCategory.Message;
    }
  }

  /// Setup notification listeners
  void _setupNotificationListeners() {
    // Listen to notification actions
    awesomeNotifications.setListeners(
      onActionReceivedMethod: _onActionReceived,
      onNotificationCreatedMethod: _onNotificationCreated,
      onNotificationDisplayedMethod: _onNotificationDisplayed,
      onDismissActionReceivedMethod: _onDismissActionReceived,
    );

    _logger.d('Notification listeners set up successfully');
  }

  /// Handle notification action received
  @pragma("vm:entry-point")
  static Future<void> _onActionReceived(ReceivedAction receivedAction) async {
    final logger = Logger();
    logger.d('Notification action received: ${receivedAction.actionType}');

    // Handle notification tap (when no button is pressed)
    if (receivedAction.buttonKeyPressed.isEmpty) {
      logger.d('Notification tapped - handling navigation');

      // Handle navigation in a separate method to avoid circular dependencies
      await _handleNotificationTapNavigation(receivedAction, logger);
      return;
    }

    // Handle action button presses
    switch (receivedAction.buttonKeyPressed) {
      case NotificationConstants.dismissAction:
        // Just dismiss the notification
        break;
      case NotificationConstants.stopAction:
        // Stop all notifications of the same type
        final payload = receivedAction.payload?['payload'];
        if (payload != null) {
          await AwesomeNotificationsManager()
              .cancelNotificationsByPayload(payload);
        }
        break;
      case NotificationConstants.snoozeAction:
        // Reschedule notification for later
        // Implementation would depend on specific requirements
        break;
    }
  }

  /// Handle notification tap navigation
  @pragma("vm:entry-point")
  static Future<void> _handleNotificationTapNavigation(
      ReceivedAction receivedAction, Logger logger) async {
    try {
      logger.d('Handling notification tap navigation');

      // Check if this is an athkar notification
      final payload = receivedAction.payload;
      final isAthkarNotification = _isAthkarNotification(payload);

      if (isAthkarNotification) {
        logger.d('Athkar notification detected - triggering navigation');

        // Use a callback mechanism to avoid direct imports
        await _triggerAthkarNavigation(receivedAction, logger);
      } else {
        logger.d('Non-athkar notification - using default navigation');
        // For other notifications, we could add specific handling here
      }
    } catch (e) {
      logger.e('Error in notification tap navigation: $e');
    }
  }

  /// Check if notification is athkar-related
  static bool _isAthkarNotification(Map<String, String?>? payload) {
    if (payload == null) return false;

    final payloadString = payload['payload'] ?? '';
    final notificationType = payload['notificationType'] ?? '';
    final athkarType = payload['athkar_type'] ?? '';

    return payloadString == 'soundNotifications' ||
        notificationType.contains('athkar') ||
        notificationType.contains('dhikr') ||
        athkarType.isNotEmpty;
  }

  /// Trigger athkar navigation using callback mechanism
  static Future<void> _triggerAthkarNavigation(
      ReceivedAction receivedAction, Logger logger) async {
    try {
      logger.d(
          'Athkar navigation triggered - payload: ${receivedAction.payload}');

      // Use the navigation callback if available
      if (_navigationCallback != null) {
        logger.d('Calling navigation callback');
        await _navigationCallback!(receivedAction);
      } else {
        logger.w('No navigation callback set - navigation will not work');
      }
    } catch (e) {
      logger.e('Error triggering athkar navigation: $e');
    }
  }

  /// Handle notification created
  @pragma("vm:entry-point")
  static Future<void> _onNotificationCreated(
      ReceivedNotification receivedNotification) async {
    final logger = Logger();
    logger.d('Notification created: ${receivedNotification.id}');
  }

  /// Handle notification displayed
  @pragma("vm:entry-point")
  static Future<void> _onNotificationDisplayed(
      ReceivedNotification receivedNotification) async {
    final logger = Logger();
    logger.d('Notification displayed: ${receivedNotification.id}');
  }

  /// Handle notification dismissed
  @pragma("vm:entry-point")
  static Future<void> _onDismissActionReceived(
      ReceivedAction receivedAction) async {
    final logger = Logger();
    logger.d('Notification dismissed: ${receivedAction.id}');
  }

  /// Open notification settings
  Future<void> openNotificationSettings() async {
    await _permissionService.openAppSettings();
  }

  /// Dispose resources
  void dispose() {
    _isInitialized = false;
    _logger.d('AwesomeNotificationsManager disposed');
  }
}
