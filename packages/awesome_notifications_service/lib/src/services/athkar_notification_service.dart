import 'package:logger/logger.dart';

import '../constants/notification_channels.dart';
import '../constants/notification_constants.dart';
import '../models/athkar_notification.dart';
import '../models/notification_data.dart';
import '../utils/notification_utils.dart';
import '../utils/sound_utils.dart';
import 'awesome_notifications_manager.dart';

/// Service for handling athkar and dhikr notifications
class AthkarNotificationService {
  static final AthkarNotificationService _instance =
      AthkarNotificationService._internal();
  factory AthkarNotificationService() => _instance;
  AthkarNotificationService._internal();

  final Logger _logger = Logger();
  final AwesomeNotificationsManager _notificationManager =
      AwesomeNotificationsManager();

  /// Schedule morning athkar notifications
  Future<bool> scheduleMorningAthkar({
    required AthkarNotificationConfig config,
    int daysAhead = 7,
    String locale = 'ar',
  }) async {
    try {
      if (!config.isEnabled) {
        _logger.d('Morning athkar notifications disabled');
        return true;
      }

      _logger.d('Scheduling morning athkar notifications');

      // Cancel existing morning athkar notifications
      await cancelAthkarNotifications(AthkarType.morning);

      int scheduledCount = 0;
      final today = DateTime.now();

      // Schedule notifications for each day
      for (int dayOffset = 0; dayOffset < daysAhead; dayOffset++) {
        final targetDate = today.add(Duration(days: dayOffset));
        final scheduledTime = _parseTimeForDate(config.time, targetDate);

        if (NotificationUtils.isValidNotificationTime(scheduledTime)) {
          final scheduled = await _scheduleAthkarNotification(
            config: config,
            scheduledTime: scheduledTime,
            locale: locale,
          );
          if (scheduled) scheduledCount++;
        }
      }

      _logger.i('Scheduled $scheduledCount morning athkar notifications');
      return scheduledCount > 0;
    } catch (e, stackTrace) {
      _logger.e('Error scheduling morning athkar',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Schedule evening athkar notifications
  Future<bool> scheduleEveningAthkar({
    required AthkarNotificationConfig config,
    int daysAhead = 7,
    String locale = 'ar',
  }) async {
    try {
      if (!config.isEnabled) {
        _logger.d('Evening athkar notifications disabled');
        return true;
      }

      _logger.d('Scheduling evening athkar notifications');

      // Cancel existing evening athkar notifications
      await cancelAthkarNotifications(AthkarType.evening);

      int scheduledCount = 0;
      final today = DateTime.now();

      // Schedule notifications for each day
      for (int dayOffset = 0; dayOffset < daysAhead; dayOffset++) {
        final targetDate = today.add(Duration(days: dayOffset));
        final scheduledTime = _parseTimeForDate(config.time, targetDate);

        if (NotificationUtils.isValidNotificationTime(scheduledTime)) {
          final scheduled = await _scheduleAthkarNotification(
            config: config,
            scheduledTime: scheduledTime,
            locale: locale,
          );
          if (scheduled) scheduledCount++;
        }
      }

      _logger.i('Scheduled $scheduledCount evening athkar notifications');
      return scheduledCount > 0;
    } catch (e, stackTrace) {
      _logger.e('Error scheduling evening athkar',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Schedule dhikr reminder notifications
  Future<bool> scheduleDhikrReminders({
    required AthkarNotificationConfig config,
    int daysAhead = 7,
    String locale = 'ar',
  }) async {
    try {
      if (!config.isEnabled || config.dhikrItems.isEmpty) {
        _logger.d('Dhikr reminders disabled or no items configured');
        return true;
      }

      _logger.d('Scheduling dhikr reminder notifications');

      // Cancel existing dhikr notifications
      await cancelAthkarNotifications(AthkarType.dhikr);

      int scheduledCount = 0;
      final today = DateTime.now();
      final startTime = _parseTimeForDate(config.time, today);

      // Schedule notifications for each day
      for (int dayOffset = 0; dayOffset < daysAhead; dayOffset++) {
        final targetDate = today.add(Duration(days: dayOffset));

        // Schedule multiple dhikr reminders throughout the day
        final dailyScheduledCount = await _scheduleDailyDhikrReminders(
          config: config,
          targetDate: targetDate,
          startTime: startTime,
          locale: locale,
        );

        scheduledCount += dailyScheduledCount;
      }

      _logger.i('Scheduled $scheduledCount dhikr reminder notifications');
      return scheduledCount > 0;
    } catch (e, stackTrace) {
      _logger.e('Error scheduling dhikr reminders',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Schedule daily dhikr reminders
  Future<int> _scheduleDailyDhikrReminders({
    required AthkarNotificationConfig config,
    required DateTime targetDate,
    required DateTime startTime,
    required String locale,
  }) async {
    int scheduledCount = 0;
    final enabledDhikrItems =
        config.dhikrItems.where((item) => item.isEnabled).toList();

    if (enabledDhikrItems.isEmpty) return 0;

    // Parse the time range from config (e.g., "07:00-22:00" or just "07:00")
    final timeRangeParts = config.time.split('-');
    final startTimeParts = timeRangeParts[0].split(':');
    final configStartHour = int.parse(startTimeParts[0]);
    final configStartMinute =
        startTimeParts.length > 1 ? int.parse(startTimeParts[1]) : 0;

    // Parse end time if provided, otherwise default to 15 hours later
    int configEndHour, configEndMinute;
    if (timeRangeParts.length > 1) {
      final endTimeParts = timeRangeParts[1].split(':');
      configEndHour = int.parse(endTimeParts[0]);
      configEndMinute =
          endTimeParts.length > 1 ? int.parse(endTimeParts[1]) : 0;
    } else {
      // Default to 15 hours later
      final endDateTime =
          DateTime(2000, 1, 1, configStartHour, configStartMinute)
              .add(Duration(hours: 15));
      configEndHour = endDateTime.hour;
      configEndMinute = endDateTime.minute;
    }

    // Calculate total duration in minutes
    final startTotalMinutes = configStartHour * 60 + configStartMinute;
    final endTotalMinutes = configEndHour * 60 + configEndMinute;
    final totalMinutesInRange = endTotalMinutes > startTotalMinutes
        ? endTotalMinutes - startTotalMinutes
        : (24 * 60) -
            startTotalMinutes +
            endTotalMinutes; // Handle overnight range

    // Calculate how many reminders to schedule throughout the day
    final intervalMinutes = config.intervalMinutes;
    final numberOfReminders = (totalMinutesInRange / intervalMinutes).floor();

    final reminderTimes = <DateTime>[];

    // Distribute reminders evenly throughout the time range with random minutes/seconds
    for (int i = 0; i < numberOfReminders; i++) {
      final minutesFromStart = i * intervalMinutes;
      final totalMinutes = configStartMinute + minutesFromStart;
      final finalHour = (configStartHour + (totalMinutes ~/ 60)) % 24;
      final finalMinute = totalMinutes % 60;

      // Add some randomness to seconds to avoid exact hour scheduling
      final randomSeconds = (i * 17 + 23) % 60; // Pseudo-random but consistent

      final reminderTime = DateTime(
        targetDate.year,
        targetDate.month,
        targetDate.day,
        finalHour,
        finalMinute,
        randomSeconds,
      );

      if (NotificationUtils.isValidNotificationTime(reminderTime)) {
        reminderTimes.add(reminderTime);
      }
    }

    // Schedule notifications for each reminder time
    for (int i = 0; i < reminderTimes.length; i++) {
      final dhikrItem = enabledDhikrItems[i % enabledDhikrItems.length];
      final reminderTime = reminderTimes[i];

      final scheduled = await _scheduleDhikrNotification(
        dhikrItem: dhikrItem,
        scheduledTime: reminderTime,
        locale: locale,
      );

      if (scheduled) scheduledCount++;
    }

    return scheduledCount;
  }

  /// Schedule a single athkar notification
  Future<bool> _scheduleAthkarNotification({
    required AthkarNotificationConfig config,
    required DateTime scheduledTime,
    required String locale,
  }) async {
    try {
      // Generate notification ID
      final notificationId = NotificationUtils.generateNotificationId(
        notificationType: _getNotificationTypeString(config.type),
        identifier: '${config.type.name}_${config.time}',
        scheduledTime: scheduledTime,
      );

      // Get notification content
      final title = _getAthkarTitle(config.type, locale);
      final body = NotificationUtils.getLocalizedNotificationBody(
        _getNotificationTypeString(config.type),
        config.type.name,
        locale: locale,
      );

      // Create notification data
      final notificationData = AthkarNotificationData(
        id: notificationId,
        title: title,
        body: body,
        scheduledTime: scheduledTime,
        channelKey: NotificationChannels.athkarChannel.channelKey!,
        notificationType: _getNotificationTypeString(config.type),
        athkarType: config.type,
        payload: NotificationConstants.soundNotificationsPayload,
        actionButtons: _createAthkarActionButtons(locale),
        customData: {
          'athkar_type': config.type.name,
          'time': config.time,
        },
      );

      // Schedule the notification
      return await _notificationManager.scheduleNotification(notificationData);
    } catch (e, stackTrace) {
      _logger.e('Error scheduling athkar notification',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Schedule a single dhikr notification
  Future<bool> _scheduleDhikrNotification({
    required DhikrItem dhikrItem,
    required DateTime scheduledTime,
    required String locale,
  }) async {
    try {
      // Generate notification ID
      final notificationId = NotificationUtils.generateNotificationId(
        notificationType: NotificationConstants.dhikrReminder,
        identifier: dhikrItem.soundFile,
        scheduledTime: scheduledTime,
      );

      // Get sound path
      final soundPath = SoundUtils.getDhikrSoundPath(dhikrItem.soundFile);

      // Create notification data
      final notificationData = AthkarNotificationData(
        id: notificationId,
        title: dhikrItem.title,
        body: dhikrItem.subtitle,
        scheduledTime: scheduledTime,
        channelKey: NotificationChannels.athkarChannel.channelKey!,
        notificationType: NotificationConstants.dhikrReminder,
        athkarType: AthkarType.dhikr,
        dhikrItem: dhikrItem,
        payload: NotificationConstants.soundNotificationsPayload,
        soundPath: soundPath,
        actionButtons: _createDhikrActionButtons(locale),
        customData: {
          'dhikr_title': dhikrItem.title,
          'dhikr_sound': dhikrItem.soundFile,
          'athkar_type':
              AthkarType.dhikr.name, // Add athkar_type for cancellation
        },
      );

      // Schedule the notification
      return await _notificationManager.scheduleNotification(notificationData);
    } catch (e, stackTrace) {
      _logger.e('Error scheduling dhikr notification',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Cancel athkar notifications by type
  Future<bool> cancelAthkarNotifications(AthkarType type) async {
    try {
      final scheduledNotifications =
          await _notificationManager.getScheduledNotifications();
      int cancelledCount = 0;

      for (final notification in scheduledNotifications) {
        final payload = notification.content?.payload?['payload'];
        if (payload == NotificationConstants.soundNotificationsPayload) {
          final athkarType = notification.content?.payload?['athkar_type'];
          if (athkarType == type.name) {
            await _notificationManager
                .cancelNotification(notification.content!.id!);
            cancelledCount++;
          }
        }
      }

      _logger.d('Cancelled $cancelledCount ${type.name} notifications');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error cancelling ${type.name} notifications',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Cancel all athkar notifications
  Future<bool> cancelAllAthkarNotifications() async {
    try {
      await _notificationManager.cancelNotificationsByPayload(
          NotificationConstants.soundNotificationsPayload);
      _logger.d('All athkar notifications cancelled');
      return true;
    } catch (e, stackTrace) {
      _logger.e('Error cancelling all athkar notifications',
          error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// Check if athkar notifications are scheduled
  Future<bool> hasAthkarNotifications() async {
    return await _notificationManager.hasScheduledNotification(
        NotificationConstants.soundNotificationsPayload);
  }

  /// Get scheduled athkar notifications count
  Future<int> getScheduledAthkarNotificationsCount() async {
    try {
      final notifications =
          await _notificationManager.getScheduledNotifications();
      return notifications.where((notification) {
        final payload = notification.content?.payload?['payload'];
        return payload == NotificationConstants.soundNotificationsPayload;
      }).length;
    } catch (e) {
      _logger.e('Error getting athkar notifications count: $e');
      return 0;
    }
  }

  /// Parse time string for specific date
  DateTime _parseTimeForDate(String timeString, DateTime date) {
    final timeParts = timeString.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);

    return DateTime(date.year, date.month, date.day, hour, minute);
  }

  /// Get notification type string
  String _getNotificationTypeString(AthkarType type) {
    switch (type) {
      case AthkarType.morning:
        return NotificationConstants.morningAthkar;
      case AthkarType.evening:
        return NotificationConstants.eveningAthkar;
      case AthkarType.dhikr:
        return NotificationConstants.dhikrReminder;
    }
  }

  /// Get athkar title
  String _getAthkarTitle(AthkarType type, String locale) {
    if (locale == 'ar') {
      switch (type) {
        case AthkarType.morning:
          return 'أذكار الصباح';
        case AthkarType.evening:
          return 'أذكار المساء';
        case AthkarType.dhikr:
          return 'تذكير بالذكر';
      }
    } else {
      switch (type) {
        case AthkarType.morning:
          return 'Morning Athkar';
        case AthkarType.evening:
          return 'Evening Athkar';
        case AthkarType.dhikr:
          return 'Dhikr Reminder';
      }
    }
  }

  /// Create action buttons for athkar notifications
  List<NotificationActionButton> _createAthkarActionButtons(String locale) {
    return [
      NotificationActionButton(
        key: NotificationConstants.dismissAction,
        label: locale == 'ar' ? 'إلغاء' : 'Dismiss',
        autoDismissible: true,
      ),
    ];
  }

  /// Create action buttons for dhikr notifications
  List<NotificationActionButton> _createDhikrActionButtons(String locale) {
    return [
      NotificationActionButton(
        key: NotificationConstants.dismissAction,
        label: locale == 'ar' ? 'إلغاء' : 'Dismiss',
        autoDismissible: true,
      ),
      NotificationActionButton(
        key: NotificationConstants.snoozeAction,
        label: locale == 'ar' ? 'تأجيل' : 'Snooze',
        autoDismissible: true,
      ),
    ];
  }
}
