import 'dart:math';

import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:logger/logger.dart';
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:timezone/timezone.dart' as tz;

import '../constants/notification_constants.dart';
import '../models/notification_data.dart';

/// Utility functions for notification handling
class NotificationUtils {
  /// Configure local timezone
  static Future<void> configureLocalTimeZone() async {
    tz_data.initializeTimeZones();
    final String timeZoneName = await FlutterTimezone.getLocalTimezone();
    tz.setLocalLocation(tz.getLocation(timeZoneName));
  }

  /// Generate unique notification ID
  static int generateNotificationId({
    required String notificationType,
    String? identifier,
    DateTime? scheduledTime,
  }) {
    int baseId;

    switch (notificationType) {
      case NotificationConstants.prayerTime:
      case NotificationConstants.prePrayerWarning:
      case NotificationConstants.iqamahTime:
        baseId = NotificationConstants.prayerNotificationBaseId;
        break;
      case NotificationConstants.morningAthkar:
      case NotificationConstants.eveningAthkar:
      case NotificationConstants.dhikrReminder:
        baseId = NotificationConstants.athkarNotificationBaseId;
        break;
      default:
        baseId = NotificationConstants.systemNotificationBaseId;
    }

    // Create a unique string combining all components
    String uniqueString = notificationType;

    if (identifier != null) {
      uniqueString += '_$identifier';
    }

    if (scheduledTime != null) {
      // Include full date and time information for uniqueness
      // Format: YYYYMMDD_HHMM to ensure different dates generate different IDs
      final dateStr = scheduledTime.year.toString().padLeft(4, '0') +
          scheduledTime.month.toString().padLeft(2, '0') +
          scheduledTime.day.toString().padLeft(2, '0');
      final timeStr = scheduledTime.hour.toString().padLeft(2, '0') +
          scheduledTime.minute.toString().padLeft(2, '0');
      uniqueString += '_${dateStr}_$timeStr';
    }

    // Generate hash from the unique string
    final hash = uniqueString.hashCode.abs();

    // Combine base ID with hash, ensuring we stay within reasonable range
    final finalId =
        baseId + (hash % 100000); // Use larger range for better distribution

    // Ensure positive ID within reasonable range
    return finalId.abs() % 2147483647; // Max int32 value
  }

  /// Check if notification time is in the future
  static bool isValidNotificationTime(DateTime scheduledTime) {
    final now = DateTime.now();
    return scheduledTime.isAfter(now);
  }

  /// Convert DateTime to TZDateTime
  static tz.TZDateTime convertToTZDateTime(DateTime dateTime) {
    return tz.TZDateTime.from(dateTime, tz.local);
  }

  /// Get next occurrence of time today or tomorrow
  static DateTime getNextOccurrence(String timeString) {
    final now = DateTime.now();
    final timeParts = timeString.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);

    var scheduledTime = DateTime(now.year, now.month, now.day, hour, minute);

    // If time has passed today, schedule for tomorrow
    if (scheduledTime.isBefore(now)) {
      scheduledTime = scheduledTime.add(const Duration(days: 1));
    }

    return scheduledTime;
  }

  /// Get notification times for multiple days ahead
  static List<DateTime> getNotificationTimesForDays({
    required DateTime baseTime,
    required int days,
  }) {
    final times = <DateTime>[];

    for (int i = 0; i < days; i++) {
      final time = baseTime.add(Duration(days: i));
      if (isValidNotificationTime(time)) {
        times.add(time);
      }
    }

    return times;
  }

  /// Format time for display
  static String formatTimeForDisplay(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Format date for display
  static String formatDateForDisplay(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = date.month.toString().padLeft(2, '0');
    final year = date.year.toString();
    return '$day/$month/$year';
  }

  /// Get localized prayer name
  static String getLocalizedPrayerName(String prayerName,
      {String locale = 'ar'}) {
    if (locale == 'ar') {
      switch (prayerName.toLowerCase()) {
        case 'fajr':
          return 'الفجر';
        case 'sunrise':
          return 'الشروق';
        case 'dhuhr':
          return 'الظهر';
        case 'asr':
          return 'العصر';
        case 'maghrib':
          return 'المغرب';
        case 'isha':
          return 'العشاء';
        case 'middle of the night':
          return 'منتصف الليل';
        case 'last third of the night':
          return 'الثلث الأخير من الليل';
        default:
          return prayerName;
      }
    }
    return prayerName;
  }

  /// Get localized notification body
  static String getLocalizedNotificationBody(
    String notificationType,
    String prayerName, {
    String locale = 'ar',
  }) {
    if (locale == 'ar') {
      switch (notificationType) {
        case NotificationConstants.prayerTime:
          // Special handling for different prayer types
          if (prayerName.toLowerCase() == 'sunrise') {
            return 'حان الآن وقت الشروق';
          } else if (prayerName.toLowerCase() == 'middle of the night') {
            return 'حان الآن وقت قيام منتصف الليل';
          } else if (prayerName.toLowerCase() == 'last third of the night') {
            return 'حان الآن وقت قيام الثلث الأخير من الليل';
          } else {
            return 'حان الآن وقت الأذان';
          }
        case NotificationConstants.prePrayerWarning:
          // Special handling for Sunnah prayers
          if (prayerName.toLowerCase() == 'middle of the night' ||
              prayerName.toLowerCase() == 'last third of the night') {
            return '15 دقيقة على وقت القيام';
          } else {
            return '15 دقيقة على الأذان';
          }
        case NotificationConstants.iqamahTime:
          return 'حان الآن وقت الإقامة';
        case NotificationConstants.morningAthkar:
          return 'حان وقت أذكار الصباح';
        case NotificationConstants.eveningAthkar:
          return 'حان وقت أذكار المساء';
        case NotificationConstants.dhikrReminder:
          return 'تذكير بالذكر';
        default:
          return 'إشعار من تطبيق صلواتي';
      }
    } else {
      switch (notificationType) {
        case NotificationConstants.prayerTime:
          // Special handling for different prayer types
          if (prayerName.toLowerCase() == 'sunrise') {
            return 'Now its time to sunrise';
          } else if (prayerName.toLowerCase() == 'middle of the night') {
            return 'Now its time for night prayer (Qiyam)';
          } else if (prayerName.toLowerCase() == 'last third of the night') {
            return 'Now its time for night prayer (Last third)';
          } else {
            return 'Now its time to adhan';
          }
        case NotificationConstants.prePrayerWarning:
          // Special handling for Sunnah prayers
          if (prayerName.toLowerCase() == 'middle of the night' ||
              prayerName.toLowerCase() == 'last third of the night') {
            return '15 minutes for night prayer';
          } else {
            return '15 minutes for Athan';
          }
        case NotificationConstants.iqamahTime:
          return 'Now Its The Iqamah Time';
        case NotificationConstants.morningAthkar:
          return 'Time for morning athkar';
        case NotificationConstants.eveningAthkar:
          return 'Time for evening athkar';
        case NotificationConstants.dhikrReminder:
          return 'Dhikr reminder';
        default:
          return 'Notification from Salawati app';
      }
    }
  }

  /// Create default action buttons for notifications
  static List<NotificationActionButton> createDefaultActionButtons({
    String locale = 'ar',
  }) {
    return [
      NotificationActionButton(
        key: NotificationConstants.dismissAction,
        label: locale == 'ar' ? 'إلغاء' : 'Dismiss',
        autoDismissible: true,
      ),
      NotificationActionButton(
        key: NotificationConstants.stopAction,
        label: locale == 'ar' ? 'إيقاف' : 'Stop',
        isDangerousOption: true,
        autoDismissible: true,
      ),
    ];
  }

  /// Validate notification data with detailed logging
  static bool validateNotificationData(NotificationData data) {
    final logger = Logger();

    // Enhanced validation with detailed logging for debugging
    logger.d('🔍 Validating notification data:');
    logger.d('   - ID: ${data.id}');
    logger.d('   - Title: "${data.title}" (length: ${data.title.length})');
    logger.d('   - Body: "${data.body}" (length: ${data.body.length})');
    logger.d(
        '   - Channel Key: "${data.channelKey}" (length: ${data.channelKey.length})');
    logger.d(
        '   - Notification Type: "${data.notificationType}" (length: ${data.notificationType.length})');
    logger.d('   - Scheduled Time: ${data.scheduledTime}');
    logger.d(
        '   - Is Valid Time: ${isValidNotificationTime(data.scheduledTime)}');
    logger.d('   - Sound Path: ${data.soundPath ?? "Not specified"}');

    // Format sound path for validation
    if (data.soundPath != null && data.soundPath!.isNotEmpty) {
      final formattedSoundPath = formatSoundPath(data.soundPath);
      logger.d('   - Formatted Sound Path: $formattedSoundPath');
    }

    if (data.title.isEmpty) {
      logger.w('❌ Validation failed: Title is empty');
      return false;
    }
    if (data.body.isEmpty) {
      logger.w('❌ Validation failed: Body is empty');
      return false;
    }
    if (data.channelKey.isEmpty) {
      logger.w('❌ Validation failed: Channel key is empty');
      return false;
    }
    if (data.notificationType.isEmpty) {
      logger.w('❌ Validation failed: Notification type is empty');
      return false;
    }
    if (!isValidNotificationTime(data.scheduledTime)) {
      logger.w(
          '❌ Validation failed: Invalid notification time (time is in the past)');
      return false;
    }

    logger.i('✅ Notification data validation passed');
    return true;
  }

  /// Calculate next prayer time
  static DateTime? getNextPrayerTime(Map<String, DateTime> prayerTimes) {
    final now = DateTime.now();
    DateTime? nextPrayer;

    for (final time in prayerTimes.values) {
      if (time.isAfter(now)) {
        if (nextPrayer == null || time.isBefore(nextPrayer)) {
          nextPrayer = time;
        }
      }
    }

    return nextPrayer;
  }

  /// Get random delay to avoid notification conflicts
  static Duration getRandomDelay({int maxSeconds = 5}) {
    final random = Random();
    return Duration(seconds: random.nextInt(maxSeconds));
  }

  /// Check if time is within business hours (for non-critical notifications)
  static bool isWithinBusinessHours(DateTime time) {
    final hour = time.hour;
    return hour >= 6 && hour <= 22; // 6 AM to 10 PM
  }

  /// Get notification priority based on type and time
  static int getNotificationPriority(
      String notificationType, DateTime scheduledTime) {
    // Higher priority for prayer times
    if (notificationType == NotificationConstants.prayerTime ||
        notificationType == NotificationConstants.iqamahTime) {
      return 10;
    }

    // Medium priority for pre-prayer warnings
    if (notificationType == NotificationConstants.prePrayerWarning) {
      return 7;
    }

    // Lower priority for athkar during night hours
    if ((notificationType == NotificationConstants.morningAthkar ||
            notificationType == NotificationConstants.eveningAthkar ||
            notificationType == NotificationConstants.dhikrReminder) &&
        !isWithinBusinessHours(scheduledTime)) {
      return 3;
    }

    // Default priority
    return 5;
  }

  /// Format sound path for awesome notifications
  /// Returns the full resource path for Android or just filename for iOS
  static String? formatSoundPath(String? soundPath) {
    if (soundPath == null || soundPath.isEmpty) {
      return null;
    }

    final logger = Logger();
    logger.d('Formatting sound path: $soundPath');

    // If the path is already a resource path, return it as-is for Android
    if (soundPath.startsWith('resource://raw/')) {
      logger.d('Sound path is already formatted for Android: $soundPath');
      return soundPath; // Return the full resource path
    }

    // Extract filename without extension
    String filename;
    if (soundPath.contains('/')) {
      filename = soundPath.split('/').last;
    } else {
      filename = soundPath;
    }

    // Remove extension if present
    if (filename.contains('.')) {
      filename = filename.split('.').first;
    }

    // Return full resource path for Android
    final formattedPath = 'resource://raw/$filename';
    logger.d('Formatted sound path: $formattedPath');
    logger.d('⚠️ Make sure the file exists:');
    logger.d('   - Android: "$filename" in android/app/src/main/res/raw/');
    logger.d('   - iOS: "$filename.wav" or "$filename.aiff" in the app bundle');

    return formattedPath;
  }
}
