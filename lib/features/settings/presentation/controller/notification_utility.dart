import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

import '../../../../core/utils/app_assets.dart';

class NotificationUtility {
  // Constants for cache keys

  final Rx<TimeOfDay> _startTime = const TimeOfDay(hour: 7, minute: 0).obs;
  final Rx<TimeOfDay> _endTime = const TimeOfDay(hour: 22, minute: 0).obs;

  NotificationUtility() {
    // Load saved time range from storage
    _loadTimeRange();
  }

  TimeOfDay get startTime => _startTime.value;
  TimeOfDay get endTime => _endTime.value;

  Future<void> saveTimeRange(TimeOfDay startTime, TimeOfDay endTime) async {
    _startTime.value = startTime;
    _endTime.value = endTime;

    // Save time range to storage
    await cacheMemory.write('startTime', {
      'hour': startTime.hour,
      'minute': startTime.minute,
    });
    await cacheMemory.write('endTime', {
      'hour': endTime.hour,
      'minute': endTime.minute,
    });
  }

  void _loadTimeRange() {
    final startTimeMap =
        cacheMemory.read('startTime') ?? {'hour': 7, 'minute': 0};
    _startTime.value = TimeOfDay(
      hour: startTimeMap['hour'],
      minute: startTimeMap['minute'],
    );

    final endTimeMap = cacheMemory.read('endTime') ?? {'hour': 22, 'minute': 0};
    _endTime.value = TimeOfDay(
      hour: endTimeMap['hour'],
      minute: endTimeMap['minute'],
    );

    // debugPrint('Loaded Start Time: ${_startTime.value.format(Get.context!)}');
    // debugPrint('Loaded End Time: ${_endTime.value.format(Get.context!)}');
  }

  List<NotificationData> prepareDailyNotifications() {
    final now = DateTime.now();
    final today = now.toLocal();

    // debugPrint('Now: $now');
    // debugPrint('Today: $today');

    final notifications = <NotificationData>[];

    // Legacy athkar notifications are no longer used - awesome notifications handle all athkar
    debugPrint(
        '🔄 Skipping legacy athkar notifications - awesome notifications handle all athkar');
    return notifications;
  }

  List<NotificationData> prepareDhikrNotifications() {
    final now = DateTime.now();
    final today = now.toLocal();

    final dhikrs = [
      Dhikr(subtitle: 'سبحان الله', sound: AppSounds.soundtasbeeh),
      Dhikr(subtitle: 'الحمدلله', sound: AppSounds.soundtahmeed),
      Dhikr(
          subtitle: 'لا حول ولا قوة الا بالله', sound: AppSounds.soundhawqalah),
      Dhikr(subtitle: 'الله أكبر', sound: AppSounds.soundtakbeer),
      Dhikr(subtitle: 'لا إله إلا الله', sound: AppSounds.soundtahleel),
      Dhikr(
          subtitle: 'أستغفر الله وأتوب اليه', sound: AppSounds.soundesteghfar),
      Dhikr(
          subtitle: 'اللهم صل وسلم وبارك على نبينا محمد',
          sound: AppSounds.soundsalah),
      Dhikr(subtitle: 'سبحان الله وبحمده', sound: AppSounds.soundtasbhamd),
      Dhikr(subtitle: 'سبحان الله العظيم', sound: AppSounds.soundtasbta3zeem),
      Dhikr(
          subtitle:
              'لا إلَه إلّا اللهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلُّ شَيْءِ قَدِيرِ',
          sound: AppSounds.soundtawheed),
      Dhikr(
          subtitle:
              'سبحان الله وبحمده عدد خلقه ورضا نفسه وزنة عرشه ومداد كلماته',
          sound: AppSounds.soundadadd),
      Dhikr(
          subtitle: 'لا إله إلا أنت سبحانك إني كنت من ظالمين',
          sound: AppSounds.soundghamm),
    ];
    final notifications = <NotificationData>[];

    // Legacy dhikr notifications are no longer used - awesome notifications handle all dhikr
    debugPrint(
        '🔄 Skipping legacy dhikr notifications - awesome notifications handle all dhikr');
    return notifications;
  }

  Future<void> scheduleNotificationsBatch(
    List<NotificationData> notifications,
    String defaultPayload,
  ) async {
    // Legacy notification scheduling is no longer used
    // All notifications are now handled by awesome_notifications
    debugPrint(
        '🔄 Legacy notification scheduling skipped - using awesome_notifications');
  }

  String _getSoundForPhrase(String phrase) {
    switch (phrase) {
      case 'سبحان الله':
        return AppSounds.soundtasbeeh;
      case 'الحمدلله':
        return AppSounds.soundtahmeed;
      case 'لا حول ولا قوة الا بالله':
        return AppSounds.soundhawqalah;
      case 'الله أكبر':
        return AppSounds.soundtakbeer;
      case 'لا إله إلا الله':
        return AppSounds.soundtahleel;
      case 'أستغفر الله وأتوب اليه':
        return AppSounds.soundesteghfar;
      case 'اللهم صل وسلم وبارك على نبينا محمد':
        return AppSounds.soundsalah;
      case 'سبحان الله وبحمده':
        return AppSounds.soundtasbhamd;
      case 'سبحان الله العظيم':
        return AppSounds.soundtasbta3zeem;
      case 'لا إلَه إلّا اللهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلُّ شَيْءِ قَدِيرِ':
        return AppSounds.soundtawheed;
      case 'سبحان الله وبحمده عدد خلقه ورضا نفسه وزنة عرشه ومداد كلماته':
        return AppSounds.soundadadd;
      case 'لا إله إلا أنت سبحانك إني كنت من ظالمين':
        return AppSounds.soundghamm;
      default:
        return AppSounds.soundtasbeeh;
    }
  }

  Future<void> pickTimeRange() async {
    TimeOfDay? startTime;
    TimeOfDay? endTime;

    // Pick start time
    await Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        title: CustomText('Choose Start Time'),
        content: CustomTimePicker(
          initialTime: TimeOfDay.now(),
          onTimeChanged: (time) {
            startTime = time;
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: CustomText('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (startTime != null) {
                Get.back();
              }
            },
            child: CustomText('Done'),
          ),
        ],
      ),
    );

    if (startTime == null) return;

    // Pick end time
    await Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        title: CustomText('Choose End Time'),
        content: StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomTimePicker(
                  initialTime: TimeOfDay(
                      hour: startTime!.hour + 1, minute: startTime!.minute),
                  onTimeChanged: (time) {
                    setState(() {
                      endTime = time;
                    });
                  },
                ),
                if (endTime != null &&
                    (endTime!.hour < startTime!.hour ||
                        (endTime!.hour == startTime!.hour &&
                            endTime!.minute <= startTime!.minute)))
                  CustomText(
                    'End Time must be after Start Time',
                    style: const TextStyle(color: Colors.red),
                  ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: CustomText('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (endTime != null &&
                  (endTime!.hour > startTime!.hour ||
                      (endTime!.hour == startTime!.hour &&
                          endTime!.minute > startTime!.minute))) {
                Get.back();
              }
            },
            child: CustomText('Done'),
          ),
        ],
      ),
    );

    if (endTime == null) return;

    // Save the time range
    await saveTimeRange(startTime!, endTime!);
    Get.snackbar('Saved'.tr, 'Time range set successfully'.tr,
        colorText: Colors.white);

    // Trigger sound athkar notification setup with new time range
    try {
      final settingsController = Get.find<SettingsController>();
      if (settingsController.isSoundAthkarNotificationOn.value) {
        await settingsController.soundsAthkarNotificationSetup();
        debugPrint('🔄 Sound athkar notifications updated with new time range');
      }
    } catch (e) {
      debugPrint('Error updating sound athkar notifications: $e');
    }
  }
}

class NotificationData {
  final String title;
  final String subtitle;
  final DateTime time;
  final String? payload;

  NotificationData({
    required this.title,
    required this.subtitle,
    required this.time,
    this.payload,
  });
}

class Dhikr {
  final String subtitle;
  final String sound;

  Dhikr({
    required this.subtitle,
    required this.sound,
  });
}

class CustomTimePicker extends StatefulWidget {
  final TimeOfDay initialTime;
  final ValueChanged<TimeOfDay> onTimeChanged;

  const CustomTimePicker({
    required this.initialTime,
    required this.onTimeChanged,
    super.key,
  });

  @override
  CustomTimePickerState createState() => CustomTimePickerState();
}

class CustomTimePickerState extends State<CustomTimePicker> {
  late int hour;
  late int minute;

  @override
  void initState() {
    super.initState();
    hour = widget.initialTime.hour;
    minute = widget.initialTime.minute;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Add the title above the picker
        // CustomText(
        //   widget.title,
        //   style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        // ),
        // const SizedBox(height: 8), // Add some spacing
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 150, // Set a fixed height for the picker
                child: CupertinoPicker(
                  backgroundColor: AppColor.kScaffoldColor,
                  itemExtent: 40,
                  onSelectedItemChanged: (index) {
                    setState(() {
                      hour = index;
                    });
                    widget.onTimeChanged(TimeOfDay(hour: hour, minute: minute));
                  },
                  children: List.generate(24, (index) {
                    return Center(
                      child: CustomText(
                        '$index',
                        style: const TextStyle(fontSize: 24),
                      ),
                    );
                  }),
                ),
              ),
            ),
            CustomText(':', style: const TextStyle(fontSize: 24)),
            Expanded(
              child: SizedBox(
                height: 150, // Set a fixed height for the picker
                child: CupertinoPicker(
                  itemExtent: 40,
                  onSelectedItemChanged: (index) {
                    setState(() {
                      minute = index;
                    });
                    widget.onTimeChanged(TimeOfDay(hour: hour, minute: minute));
                  },
                  children: List.generate(60, (index) {
                    return Center(
                      child: CustomText(
                        '$index',
                        style: const TextStyle(fontSize: 24),
                      ),
                    );
                  }),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class TimeRangePicker extends StatelessWidget {
  final NotificationUtility notificationUtility =
      Get.put(NotificationUtility());

  TimeRangePicker({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        IconButton.filled(
          icon: const Icon(Icons.settings),
          onPressed: () async {
            await notificationUtility.pickTimeRange();
          },
        ),
        // const SizedBox(height: 16),
        // Obx(() {
        //   return CustomText(
        //     'النطاق الزمني: ${notificationUtility.startTime.format(context)} - ${notificationUtility.endTime.format(context)}',
        //     style: const TextStyle(fontSize: 16),
        //   );
        // }),
      ],
    );
  }
}
