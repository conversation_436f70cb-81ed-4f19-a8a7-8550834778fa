import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';

import '../../../../core/utils/app_assets.dart';

class NotificationUtility {
  // Constants for cache keys

  final Rx<TimeOfDay> _startTime = const TimeOfDay(hour: 7, minute: 0).obs;
  final Rx<TimeOfDay> _endTime = const TimeOfDay(hour: 22, minute: 0).obs;

  NotificationUtility() {
    // Load saved time range from storage
    _loadTimeRange();
  }

  TimeOfDay get startTime => _startTime.value;
  TimeOfDay get endTime => _endTime.value;

  Future<void> saveTimeRange(TimeOfDay startTime, TimeOfDay endTime) async {
    _startTime.value = startTime;
    _endTime.value = endTime;

    // Save time range to storage
    await cacheMemory.write('startTime', {
      'hour': startTime.hour,
      'minute': startTime.minute,
    });
    await cacheMemory.write('endTime', {
      'hour': endTime.hour,
      'minute': endTime.minute,
    });
  }

  void _loadTimeRange() {
    final startTimeMap =
        cacheMemory.read('startTime') ?? {'hour': 7, 'minute': 0};
    _startTime.value = TimeOfDay(
      hour: startTimeMap['hour'],
      minute: startTimeMap['minute'],
    );

    final endTimeMap = cacheMemory.read('endTime') ?? {'hour': 22, 'minute': 0};
    _endTime.value = TimeOfDay(
      hour: endTimeMap['hour'],
      minute: endTimeMap['minute'],
    );

    // debugPrint('Loaded Start Time: ${_startTime.value.format(Get.context!)}');
    // debugPrint('Loaded End Time: ${_endTime.value.format(Get.context!)}');
  }

  List<NotificationData> prepareDailyNotifications() {
    final now = DateTime.now();
    final today = now.toLocal();

    // debugPrint('Now: $now');
    // debugPrint('Today: $today');

    final notifications = <NotificationData>[];

    // Legacy athkar notifications are no longer used - awesome notifications handle all athkar
    debugPrint(
        '🔄 Skipping legacy athkar notifications - awesome notifications handle all athkar');
    return notifications;
  }

  List<NotificationData> prepareDhikrNotifications() {
    final now = DateTime.now();
    final today = now.toLocal();

    final dhikrs = [
      Dhikr(subtitle: 'سبحان الله', sound: AppSounds.soundtasbeeh),
      Dhikr(subtitle: 'الحمدلله', sound: AppSounds.soundtahmeed),
      Dhikr(
          subtitle: 'لا حول ولا قوة الا بالله', sound: AppSounds.soundhawqalah),
      Dhikr(subtitle: 'الله أكبر', sound: AppSounds.soundtakbeer),
      Dhikr(subtitle: 'لا إله إلا الله', sound: AppSounds.soundtahleel),
      Dhikr(
          subtitle: 'أستغفر الله وأتوب اليه', sound: AppSounds.soundesteghfar),
      Dhikr(
          subtitle: 'اللهم صل وسلم وبارك على نبينا محمد',
          sound: AppSounds.soundsalah),
      Dhikr(subtitle: 'سبحان الله وبحمده', sound: AppSounds.soundtasbhamd),
      Dhikr(subtitle: 'سبحان الله العظيم', sound: AppSounds.soundtasbta3zeem),
      Dhikr(
          subtitle:
              'لا إلَه إلّا اللهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلُّ شَيْءِ قَدِيرِ',
          sound: AppSounds.soundtawheed),
      Dhikr(
          subtitle:
              'سبحان الله وبحمده عدد خلقه ورضا نفسه وزنة عرشه ومداد كلماته',
          sound: AppSounds.soundadadd),
      Dhikr(
          subtitle: 'لا إله إلا أنت سبحانك إني كنت من ظالمين',
          sound: AppSounds.soundghamm),
    ];
    final notifications = <NotificationData>[];

    // Legacy dhikr notifications are no longer used - awesome notifications handle all dhikr
    debugPrint(
        '🔄 Skipping legacy dhikr notifications - awesome notifications handle all dhikr');
    return notifications;
  }

  Future<void> scheduleNotificationsBatch(
    List<NotificationData> notifications,
    String defaultPayload,
  ) async {
    // Legacy notification scheduling is no longer used
    // All notifications are now handled by awesome_notifications
    debugPrint(
        '🔄 Legacy notification scheduling skipped - using awesome_notifications');
  }

  String _getSoundForPhrase(String phrase) {
    switch (phrase) {
      case 'سبحان الله':
        return AppSounds.soundtasbeeh;
      case 'الحمدلله':
        return AppSounds.soundtahmeed;
      case 'لا حول ولا قوة الا بالله':
        return AppSounds.soundhawqalah;
      case 'الله أكبر':
        return AppSounds.soundtakbeer;
      case 'لا إله إلا الله':
        return AppSounds.soundtahleel;
      case 'أستغفر الله وأتوب اليه':
        return AppSounds.soundesteghfar;
      case 'اللهم صل وسلم وبارك على نبينا محمد':
        return AppSounds.soundsalah;
      case 'سبحان الله وبحمده':
        return AppSounds.soundtasbhamd;
      case 'سبحان الله العظيم':
        return AppSounds.soundtasbta3zeem;
      case 'لا إلَه إلّا اللهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلُّ شَيْءِ قَدِيرِ':
        return AppSounds.soundtawheed;
      case 'سبحان الله وبحمده عدد خلقه ورضا نفسه وزنة عرشه ومداد كلماته':
        return AppSounds.soundadadd;
      case 'لا إله إلا أنت سبحانك إني كنت من ظالمين':
        return AppSounds.soundghamm;
      default:
        return AppSounds.soundtasbeeh;
    }
  }

  Future<void> pickTimeRange() async {
    TimeOfDay? startTime;
    TimeOfDay? endTime;

    // Pick start time
    await Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        title: CustomText('Choose Start Time'),
        content: CustomTimePicker(
          initialTime: TimeOfDay.now(),
          onTimeChanged: (time) {
            startTime = time;
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: CustomText('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (startTime != null) {
                Get.back();
              }
            },
            child: CustomText('Done'),
          ),
        ],
      ),
    );

    if (startTime == null) return;

    // Pick end time
    await Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        title: CustomText('Choose End Time'),
        content: StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomTimePicker(
                  initialTime: TimeOfDay(
                      hour: startTime!.hour + 1, minute: startTime!.minute),
                  onTimeChanged: (time) {
                    setState(() {
                      endTime = time;
                    });
                  },
                ),
                if (endTime != null &&
                    (endTime!.hour < startTime!.hour ||
                        (endTime!.hour == startTime!.hour &&
                            endTime!.minute <= startTime!.minute)))
                  CustomText(
                    'End Time must be after Start Time',
                    style: const TextStyle(color: Colors.red),
                  ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back();
            },
            child: CustomText('Cancel'),
          ),
          TextButton(
            onPressed: () {
              if (endTime != null &&
                  (endTime!.hour > startTime!.hour ||
                      (endTime!.hour == startTime!.hour &&
                          endTime!.minute > startTime!.minute))) {
                Get.back();
              }
            },
            child: CustomText('Done'),
          ),
        ],
      ),
    );

    if (endTime == null) return;

    // Save the time range
    await saveTimeRange(startTime!, endTime!);
    Get.snackbar('Saved'.tr, 'Time range set successfully'.tr,
        colorText: Colors.white);

    // Trigger sound athkar notification setup with new time range
    try {
      final settingsController = Get.find<SettingsController>();
      if (settingsController.isSoundAthkarNotificationOn.value) {
        await settingsController.soundsAthkarNotificationSetup();
        debugPrint('🔄 Sound athkar notifications updated with new time range');
      }
    } catch (e) {
      debugPrint('Error updating sound athkar notifications: $e');
    }
  }
}

class NotificationData {
  final String title;
  final String subtitle;
  final DateTime time;
  final String? payload;

  NotificationData({
    required this.title,
    required this.subtitle,
    required this.time,
    this.payload,
  });
}

class Dhikr {
  final String subtitle;
  final String sound;

  Dhikr({
    required this.subtitle,
    required this.sound,
  });
}

class CustomTimePicker extends StatefulWidget {
  final TimeOfDay initialTime;
  final ValueChanged<TimeOfDay> onTimeChanged;

  const CustomTimePicker({
    required this.initialTime,
    required this.onTimeChanged,
    super.key,
  });

  @override
  CustomTimePickerState createState() => CustomTimePickerState();
}

class CustomTimePickerState extends State<CustomTimePicker> {
  late int hour;
  late int minute;

  @override
  void initState() {
    super.initState();
    hour = widget.initialTime.hour;
    minute = widget.initialTime.minute;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // Add the title above the picker
        // CustomText(
        //   widget.title,
        //   style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        // ),
        // const SizedBox(height: 8), // Add some spacing
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 150, // Set a fixed height for the picker
                child: CupertinoPicker(
                  backgroundColor: AppColor.kScaffoldColor,
                  itemExtent: 40,
                  onSelectedItemChanged: (index) {
                    setState(() {
                      hour = index;
                    });
                    widget.onTimeChanged(TimeOfDay(hour: hour, minute: minute));
                  },
                  children: List.generate(24, (index) {
                    return Center(
                      child: CustomText(
                        '$index',
                        style: const TextStyle(fontSize: 24),
                      ),
                    );
                  }),
                ),
              ),
            ),
            CustomText(':', style: const TextStyle(fontSize: 24)),
            Expanded(
              child: SizedBox(
                height: 150, // Set a fixed height for the picker
                child: CupertinoPicker(
                  itemExtent: 40,
                  onSelectedItemChanged: (index) {
                    setState(() {
                      minute = index;
                    });
                    widget.onTimeChanged(TimeOfDay(hour: hour, minute: minute));
                  },
                  children: List.generate(60, (index) {
                    return Center(
                      child: CustomText(
                        '$index',
                        style: const TextStyle(fontSize: 24),
                      ),
                    );
                  }),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class TimeRangePicker extends StatelessWidget {
  final NotificationUtility notificationUtility =
      Get.put(NotificationUtility());

  TimeRangePicker({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final startTime = notificationUtility.startTime;
      final endTime = notificationUtility.endTime;

      // Format time strings with localization
      final startTimeStr = NumberLocalization.localizeTimeString(
          '${startTime.hour.toString().padLeft(2, '0')}:${startTime.minute.toString().padLeft(2, '0')}');
      final endTimeStr = NumberLocalization.localizeTimeString(
          '${endTime.hour.toString().padLeft(2, '0')}:${endTime.minute.toString().padLeft(2, '0')}');

      return Column(
        children: [
          // Settings button
          IconButton.filled(
            icon: const Icon(Icons.schedule, size: 20),
            style: IconButton.styleFrom(
              backgroundColor: AppColor.kGreenColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.all(8),
            ),
            onPressed: () async {
              await _showTimeRangeDialog(context);
            },
          ),
          4.verticalSpace,
          // Time range display
          CustomText(
            '$startTimeStr - $endTimeStr',
            style: TextStyle(
              fontSize: 10.sp,
              color: AppColor.kWhiteColor.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      );
    });
  }

  Future<void> _showTimeRangeDialog(BuildContext context) async {
    TimeOfDay? selectedStartTime = notificationUtility.startTime;
    TimeOfDay? selectedEndTime = notificationUtility.endTime;

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              backgroundColor: AppColor.kScaffoldColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              title: Row(
                children: [
                  Icon(Icons.schedule, color: AppColor.kGreenColor),
                  8.horizontalSpace,
                  CustomText(
                    'Audio Athkar Time Range'.tr,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              content: SizedBox(
                width: double.maxFinite,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomText(
                      'Choose when you want to receive audio athkar reminders throughout the day.'
                          .tr,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColor.kWhiteColor.withOpacity(0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    24.verticalSpace,

                    // Start Time
                    _buildTimeSelector(
                      context: context,
                      title: 'Start Time'.tr,
                      time: selectedStartTime!,
                      onTimeChanged: (newTime) {
                        setState(() {
                          selectedStartTime = newTime;
                        });
                      },
                    ),

                    16.verticalSpace,

                    // End Time
                    _buildTimeSelector(
                      context: context,
                      title: 'End Time'.tr,
                      time: selectedEndTime!,
                      onTimeChanged: (newTime) {
                        setState(() {
                          selectedEndTime = newTime;
                        });
                      },
                    ),

                    16.verticalSpace,

                    // Info text
                    Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColor.kGreenColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppColor.kGreenColor.withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: AppColor.kGreenColor,
                            size: 16,
                          ),
                          8.horizontalSpace,
                          Expanded(
                            child: CustomText(
                              'Audio reminders will be distributed evenly throughout this time range.'
                                  .tr,
                              style: TextStyle(
                                fontSize: 10.sp,
                                color: AppColor.kGreenColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: CustomText(
                    'Cancel'.tr,
                    style:
                        TextStyle(color: AppColor.kWhiteColor.withOpacity(0.6)),
                  ),
                ),
                ElevatedButton(
                  onPressed: () async {
                    // Validate time range
                    if (_isValidTimeRange(
                        selectedStartTime!, selectedEndTime!)) {
                      await notificationUtility.saveTimeRange(
                          selectedStartTime!, selectedEndTime!);
                      Navigator.pop(context);

                      // Show success message
                      Get.snackbar(
                        'Success'.tr,
                        'Time range updated successfully'.tr,
                        backgroundColor: AppColor.kGreenColor,
                        colorText: Colors.white,
                        duration: Duration(seconds: 2),
                      );

                      // Update notifications if audio athkar is enabled
                      try {
                        final settingsController =
                            Get.find<SettingsController>();
                        if (settingsController
                            .isSoundAthkarNotificationOn.value) {
                          await settingsController
                              .soundsAthkarNotificationSetup();
                        }
                      } catch (e) {
                        debugPrint('Error updating notifications: $e');
                      }
                    } else {
                      Get.snackbar(
                        'Invalid Time Range'.tr,
                        'End time must be after start time'.tr,
                        backgroundColor: Colors.red,
                        colorText: Colors.white,
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColor.kGreenColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: CustomText('Save'.tr),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildTimeSelector({
    required BuildContext context,
    required String title,
    required TimeOfDay time,
    required Function(TimeOfDay) onTimeChanged,
  }) {
    final timeStr = NumberLocalization.localizeTimeString(
        '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}');

    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColor.kRectangleColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColor.kGreenColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  title,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColor.kWhiteColor.withOpacity(0.7),
                  ),
                ),
                4.verticalSpace,
                CustomText(
                  timeStr,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColor.kGreenColor,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () async {
              final newTime = await showTimePicker(
                context: context,
                initialTime: time,
                builder: (context, child) {
                  return Theme(
                    data: Theme.of(context).copyWith(
                      timePickerTheme: TimePickerThemeData(
                        backgroundColor: AppColor.kScaffoldColor,
                        hourMinuteTextColor: Colors.white,
                        hourMinuteColor: AppColor.kRectangleColor,
                        dialHandColor: AppColor.kGreenColor,
                        dialBackgroundColor: AppColor.kRectangleColor,
                        entryModeIconColor: AppColor.kGreenColor,
                      ),
                    ),
                    child: child!,
                  );
                },
              );
              if (newTime != null) {
                onTimeChanged(newTime);
              }
            },
            icon: Icon(
              Icons.edit,
              color: AppColor.kGreenColor,
              size: 20,
            ),
          ),
        ],
      ),
    );
  }

  bool _isValidTimeRange(TimeOfDay start, TimeOfDay end) {
    final startMinutes = start.hour * 60 + start.minute;
    final endMinutes = end.hour * 60 + end.minute;
    return endMinutes > startMinutes;
  }
}
