import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_consts.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/settings/data/models/adhan_model.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/iqamah_dialog.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_divider.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_switch_builder.dart';

import '../screens/notification/athan_sound_screen.dart';

class PrayerNotificationWidget extends StatelessWidget {
  final String title;

  const PrayerNotificationWidget({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    DateTime dateTime = AppFunctions.getPrayerTime(
        title, Get.find<PrayerController>().prayerTimes.value!);
    String adhanTime = AppFunctions.formatTime(dateTime);
    SettingsController controller = SettingsController.instance;
    AdhanModel currentAdhan = controller.athanNotificationsMap[title]!;

    return Column(
      children: [
        SmoothEdgesContainer(
          borderRadius: BorderRadius.circular(60.r),
          padding: EdgeInsets.all(24.w),
          color: AppColor.kRectangleColor,
          child: Column(
            children: [
              // SettingsSwitchItemBuilder(
              //   title: title,
              //   switchValue: currentAdhan.isNotified,
              //   onChanged: (value) {
              //     currentAdhan.isNotified = value;
              //     controller.setNotification(currentAdhan, title);
              //   },
              // ),
              // Remove the Obx widget and replace with a simpler switch
              SettingsSwitchItemBuilder(
                title: title,
                switchValue: currentAdhan.isNotified,
                onChanged: (value) {
                  currentAdhan.isNotified = value;
                  controller.setNotification(currentAdhan, title);
                  if (value) {
                    Get.dialog(
                      AthanSoundSelectionDialog(prayerKey: title),
                    );
                  }
                },
              ),
              if (title != SUNRISE) ...[
                // 4.verticalSpace,
                // const SettingsDivider(),
                // 16.verticalSpace,
                // SettingsSwitchItemBuilder(
                //   title: 'Full Athan',
                //   switchValue: currentAdhan.isFullAdhan,
                //   onChanged: (value) {
                //     currentAdhan.isFullAdhan = value;
                //     controller.setNotification(currentAdhan);
                //   },
                // ),
                4.verticalSpace,
                const SettingsDivider(),
                16.verticalSpace,
                SettingsSwitchItemBuilder(
                  title: 'Pre-Notification',
                  switchValue: currentAdhan.isPreNotified,
                  onChanged: (value) {
                    currentAdhan.isPreNotified = value;
                    controller.setNotification(currentAdhan, title);
                  },
                ),
                4.verticalSpace,
                const SettingsDivider(),
                16.verticalSpace,
                SettingsSwitchItemBuilder(
                  title: 'Iqamah-Notification',
                  subtitle: currentAdhan.iqamah != null
                      ? '${currentAdhan.iqamah!.toInt()} ${'Minutes'.tr}'
                      : null,
                  switchValue: currentAdhan.iqamah != null,
                  onChanged: (value) {
                    if (value) {
                      Get.dialog(IqamahDialog(
                        currentAdhan: currentAdhan,
                        controller: controller,
                        prayerKey: title,
                      ));
                    } else {
                      currentAdhan.iqamah = null;
                      controller.setNotification(currentAdhan, title);
                    }
                  },
                ),
              ],
            ],
          ),
        ),
        16.verticalSpace,
        CustomText(
          '${'You will be notified by end of the day at'.tr} $adhanTime',
          style: TextStyle(
              fontSize: 12.sp, color: AppColor.kWhiteColor.withOpacity(0.6)),
        ),
      ],
    );
  }
}

class PrayerNotificationWidgetForHome extends StatelessWidget {
  final String title;

  const PrayerNotificationWidgetForHome({super.key, required this.title});

  String _getAthanName(AthanSoundType type) {
    final sounds = SoundUtils.getAvailableAthanSounds();
    final soundInfo = sounds.firstWhere(
      (sound) => sound.type == type,
      orElse: () => sounds.first,
    );
    return Get.locale?.languageCode == 'ar' ? soundInfo.name : soundInfo.nameEn;
  }

  @override
  Widget build(BuildContext context) {
    DateTime dateTime = AppFunctions.getPrayerTime(
        title, Get.find<PrayerController>().prayerTimes.value!);
    String adhanTime = AppFunctions.formatTime(dateTime);
    SettingsController controller = SettingsController.instance;
    AdhanModel currentAdhan = controller.athanNotificationsMap[title]!;
    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  CustomText(
                    'Notification Settings:',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  6.horizontalSpace,
                  CustomText(
                    title.tr,
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              InkWell(
                  onTap: () => Get.back(),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SvgPicture.asset(AppSvgs.kClose),
                  )),
            ],
          ),
          12.verticalSpace,
          SmoothEdgesContainer(
            borderRadius: BorderRadius.circular(60.r),
            padding: EdgeInsets.all(24.w),
            color: AppColor.kRectangleColor,
            child: Column(
              children: [
                SettingsSwitchItemBuilder(
                  title: title,
                  switchValue: currentAdhan.isNotified,
                  subtitle: currentAdhan.isNotified
                      ? currentAdhan.isSilent
                          ? 'Use Default'.tr
                          : _getAthanName(
                              AthanSoundType.values[currentAdhan.soundIndex])
                      : null,
                  onChanged: (value) {
                    Get.log(
                        "current Value:  $value currentAdhan.title: ${currentAdhan.title} $MIDDLEOFTHENIGHT");
                    currentAdhan.isNotified = value;
                    controller.setNotification(currentAdhan, title);

                    // Show sound selection dialog when notification is enabled

                    if (value && (currentAdhan.title != SUNRISE)) {
                      Get.dialog(
                        AthanSoundSelectionDialog(prayerKey: title),
                      );
                    } else {
                      // If notification is disabled, reset sound to default
                      currentAdhan.isSilent = true;
                      currentAdhan.soundIndex = 0;
                      controller.setNotification(currentAdhan, title);
                    }
                  },
                ),
                // SettingsSwitchItemBuilder(
                //   title: 'Enable Notification',
                //   switchValue: currentAdhan.isNotified,
                //   onChanged: (value) {
                //     currentAdhan.isNotified = value;
                //     controller.setNotification(currentAdhan, title);
                //   },
                // ),
                if (title != SUNRISE) ...[
                  // 4.verticalSpace,
                  // const SettingsDivider(),
                  // 16.verticalSpace,
                  // SettingsSwitchItemBuilder(
                  //   title: 'Full Athan',
                  //   switchValue: currentAdhan.isFullAdhan,
                  //   onChanged: (value) {
                  //     currentAdhan.isFullAdhan = value;
                  //     controller.setNotification(currentAdhan);
                  //   },
                  // ),
                  4.verticalSpace,
                  const SettingsDivider(),
                  16.verticalSpace,
                  SettingsSwitchItemBuilder(
                    title: 'Pre-Notification',
                    switchValue: currentAdhan.isPreNotified,
                    onChanged: (value) {
                      currentAdhan.isPreNotified = value;
                      controller.setNotification(currentAdhan, title);
                    },
                  ),
                  4.verticalSpace,
                  const SettingsDivider(),
                  16.verticalSpace,
                  SettingsSwitchItemBuilder(
                    title: 'Iqamah-Notification',
                    subtitle: currentAdhan.iqamah != null
                        ? '${currentAdhan.iqamah!.toInt()} ${'Minutes'.tr}'
                        : null,
                    switchValue: currentAdhan.iqamah != null,
                    onChanged: (value) {
                      if (value) {
                        Get.dialog(IqamahDialog(
                          currentAdhan: currentAdhan,
                          controller: controller,
                          prayerKey: title,
                        ));
                      } else {
                        currentAdhan.iqamah = null;
                        controller.setNotification(currentAdhan, title);
                      }
                    },
                  ),
                  // Obx(() {
                  //   final model = controller.athanNotificationsMap[title]!;
                  //   // if not silent, switch is ON; if silent, switch is OFF
                  //   return SettingsSwitchItemBuilder(
                  //     title: 'Adhan Sound'.tr,
                  //     subtitle: model.isSilent
                  //         ? 'Use Default'.tr
                  //         : getAdhanName(
                  //             AthanSoundType.values[model.soundIndex]),
                  //     switchValue: !model.isSilent,
                  //     onChanged: (value) {
                  //       if (value) {
                  //         // turning ON → pick a custom sound
                  //         Get.dialog(AthanSoundDialog(prayerKey: title));
                  //       } else {
                  //         // turning OFF → silent
                  //         model.isSilent = true;
                  //         controller.setAdhanSoundToDefaultNotification(true);
                  //         controller.setNotification(model, title);
                  //       }
                  //     },
                  //   );
                  // })
                ],
              ],
            ),
          ),
          16.verticalSpace,
          CustomText(
            '${'You will be notified by end of the day at'.tr} $adhanTime',
            style: TextStyle(
                fontSize: 12.sp, color: AppColor.kWhiteColor.withOpacity(0.6)),
          ),
          // 40.verticalSpace,
        ],
      ),
    );
  }
}

class SunnahPrayerNotificationWidgetForHome extends StatelessWidget {
  final String title;

  const SunnahPrayerNotificationWidgetForHome({super.key, required this.title});

  String _getAthanName(AthanSoundType type) {
    final sounds = SoundUtils.getAvailableAthanSounds();
    final soundInfo = sounds.firstWhere(
      (sound) => sound.type == type,
      orElse: () => sounds.first,
    );
    return Get.locale?.languageCode == 'ar' ? soundInfo.name : soundInfo.nameEn;
  }

  @override
  Widget build(BuildContext context) {
    DateTime dateTime = AppFunctions.getSunnahPrayerTime(
        title, Get.find<PrayerController>().sunnahTimes.value!);
    String adhanTime = AppFunctions.formatTime(dateTime);
    SettingsController controller = SettingsController.instance;
    AdhanModel currentAdhan = controller.athanNotificationsMap[title]!;
    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  CustomText(
                    'Notification Settings:',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  6.horizontalSpace,
                  // CustomText(
                  //   title.tr,
                  //   style: TextStyle(
                  //     fontSize: 18.sp,
                  //     fontWeight: FontWeight.bold,
                  //   ),
                  // ),
                ],
              ),
              InkWell(
                  onTap: () => Get.back(),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SvgPicture.asset(AppSvgs.kClose),
                  )),
            ],
          ),
          12.verticalSpace,
          SmoothEdgesContainer(
            borderRadius: BorderRadius.circular(60.r),
            padding: EdgeInsets.all(16.w),
            color: AppColor.kRectangleColor,
            child: Column(
              children: [
                SettingsSwitchItemBuilder(
                  title: title,
                  switchValue: currentAdhan.isNotified,
                  subtitle: currentAdhan.isNotified
                      ? currentAdhan.isSilent
                          ? 'Use Default'.tr
                          : _getAthanName(
                              AthanSoundType.values[currentAdhan.soundIndex])
                      : null,
                  onChanged: (value) {
                    currentAdhan.isNotified = value;
                    currentAdhan.isSilent = true;
                    controller.setNotification(currentAdhan, title);
                  },
                ),
                // SettingsSwitchItemBuilder(
                //   title: 'Enable Notification',
                //   switchValue: currentAdhan.isNotified,
                //   onChanged: (value) {
                //     currentAdhan.isNotified = value;
                //     controller.setNotification(currentAdhan, title);
                //   },
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// class AdhanSoundSelector extends StatefulWidget {
//   final Function(AthanSoundType) onChanged;
//   final String title;
//   final AthanSoundType initialSelection;

//   const AdhanSoundSelector(
//       {super.key,
//       required this.title,
//       required this.onChanged,
//       required this.initialSelection});

//   @override
//   AdhanSoundSelectorState createState() => AdhanSoundSelectorState();
// }

// class AdhanSoundSelectorState extends State<AdhanSoundSelector> {
//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         Expanded(child: CustomText(widget.title)),
//         Expanded(
//           child: CustomAnimatedDropdown<AthanSoundType>(
//             expandedFillColor: AppColor.kScaffoldColor,
//             hintText: 'قم باختيار صوت الاذان',
//             headerBuilder: (p0, p1, p2) {
//               return CustomText(getAdhanName(p1));
//             },
//             onChanged: (value) {
//               if (value != null) {
//                 widget.onChanged(value);
//               }
//             },
//             initialItem: widget.initialSelection,
//             items: AthanSoundType.values
//                 .map((AthanSoundType type) => type)
//                 .toList(),
//             listItemBuilder: (context, item, isSelected, onItemSelect) {
//               return CustomText(getAdhanName(item));
//             },
//           ),
//         ),
//       ],
//     );
//   }
// }
