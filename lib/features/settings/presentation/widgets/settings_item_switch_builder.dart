import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/custom_text.dart';

class SettingsSwitchItemBuilder extends StatefulWidget {
  final String title;
  final Future<void> Function(bool)? onChanged;
  final bool switchValue;
  final String? subtitle;
  final String? svg;
  final CrossAxisAlignment rowCrossAxisAlignment;
  final bool isEnabled;
  final VoidCallback? onPermissionRequested;
  final bool showLoadingOnChange;

  const SettingsSwitchItemBuilder({
    super.key,
    required this.title,
    this.onChanged,
    this.switchValue = true,
    this.subtitle,
    this.svg,
    this.rowCrossAxisAlignment = CrossAxisAlignment.center,
    this.isEnabled = true,
    this.onPermissionRequested,
    this.showLoadingOnChange = false,
  });

  @override
  State<SettingsSwitchItemBuilder> createState() =>
      _SettingsSwitchItemBuilderState();
}

class _SettingsSwitchItemBuilderState extends State<SettingsSwitchItemBuilder> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: widget.rowCrossAxisAlignment,
      children: [
        if (widget.svg != null) ...[
          SvgPicture.asset(widget.svg!),
          8.horizontalSpace,
        ],
        Expanded(
          flex: 4,
          child: widget.subtitle == null
              ? CustomText(widget.title)
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(widget.title),
                    4.verticalSpace,
                    CustomText(
                      widget.subtitle!,
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: AppColor.kWhiteColor.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
        ),
        18.horizontalSpace,
        Expanded(
          child: _isLoading && widget.showLoadingOnChange
              ? SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor:
                        AlwaysStoppedAnimation<Color>(AppColor.kGreenColor),
                  ),
                )
              : Switch(
                  value: widget.switchValue,
                  onChanged: _isLoading
                      ? null
                      : (value) async {
                          if (widget.isEnabled) {
                            if (widget.showLoadingOnChange) {
                              setState(() {
                                _isLoading = true;
                              });
                            }

                            try {
                              await widget.onChanged?.call(value);
                            } finally {
                              if (mounted && widget.showLoadingOnChange) {
                                setState(() {
                                  _isLoading = false;
                                });
                              }
                            }
                          } else {
                            widget.onPermissionRequested?.call();
                          }
                        },
                  activeTrackColor: widget.isEnabled
                      ? AppColor.kGreenColor
                      : AppColor.kGreenColor.withOpacity(0.5),
                  thumbColor: WidgetStateProperty.resolveWith<Color?>(
                    (states) => widget.isEnabled
                        ? AppColor.kWhiteColor
                        : AppColor.kWhiteColor.withOpacity(0.5),
                  ),
                ),
        ),
      ],
    );
  }
}
