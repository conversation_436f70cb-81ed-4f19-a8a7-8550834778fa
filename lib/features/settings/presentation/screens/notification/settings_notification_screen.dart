import 'package:app_settings/app_settings.dart';
import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:salawati/core/utils/app_assets.dart';
import 'package:salawati/core/utils/app_color.dart';
import 'package:salawati/core/utils/app_functions.dart';
import 'package:salawati/core/utils/app_router.dart';
import 'package:salawati/core/utils/custom_text.dart';
import 'package:salawati/core/widgets/smooth_edges_container.dart';
import 'package:salawati/features/prayer/presentation/controller/prayer_controller.dart';
import 'package:salawati/features/settings/presentation/controller/notification_utility.dart';
import 'package:salawati/features/settings/presentation/controller/settings_controller.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_app_bar.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_item_switch_builder.dart';
import 'package:salawati/features/settings/presentation/widgets/settings_main_background.dart';

class SettingsNotificationScreen extends GetView<SettingsController> {
  const SettingsNotificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // final SettingsController settingsController = Get.find();

    final NotificationPermissionController permissionController =
        Get.put(NotificationPermissionController());

    // Call the permission check once when the screen is initialized
    permissionController.checkAndRequestPermission();

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Image.asset(
              AppImages.kMainbg,
              height: double.infinity,
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            SingleChildScrollView(
              child: Column(
                children: [
                  const SettingsMainBackground(),
                  SmoothEdgesContainer(
                    borderRadius: BorderRadius.circular(60.r),
                    width: double.infinity,
                    color: Theme.of(context).primaryColor,
                    child: Column(
                      children: [
                        48.verticalSpace,
                        const SettingsAppBar(
                            title: 'Settings', subtitle: 'Notifications'),
                        24.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          child: Column(
                            children: [
                              _buildAthanSettings(controller),
                              16.verticalSpace,
                              _buildNotificationSettings(
                                  controller, permissionController),
                              32.verticalSpace,
                            ],
                          ),
                        ),
                        0.3.sh.verticalSpace,
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAthanSettings(SettingsController controller) {
    return SmoothEdgesContainer(
      borderRadius: BorderRadius.circular(60.r),
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 8.h),
      color: AppColor.kRectangleColor,
      child: Column(
        children: [
          SettingsItemBuilder(
            title: 'Athan',
            route: AppRouter.kNotificationAthanScreen,
            whenComplete: () =>
                Get.find<PrayerController>().athanNotificationSetup(),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettings(
    SettingsController controller,
    NotificationPermissionController permissionController,
  ) {
    return SmoothEdgesContainer(
      borderRadius: BorderRadius.circular(60.r),
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 8.h),
      color: AppColor.kRectangleColor,
      child: GetBuilder<SettingsController>(
        builder: (controller) {
          return Column(
            children: [
              16.verticalSpace,
              16.verticalSpace,
              _buildAudioAthkarSwitch(controller, permissionController),
              16.verticalSpace,
              _buildMorningAthkarSwitch(controller, permissionController),
              16.verticalSpace,
              _buildEveningAthkarSwitch(controller, permissionController),
              16.verticalSpace,
              _buildTestDhikrButton(controller),
              16.verticalSpace,
              // _buildHasibNafisSwitch(controller, permissionController),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAudioAthkarSwitch(
    SettingsController controller,
    NotificationPermissionController permissionController,
  ) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: buildPermissionBasedSwitch(
                title: 'Audio Athkar'.tr,
                subtitle:
                    'Listen to blessed audio Athkar reminders throughout the day to help you remember Allah.'
                        .tr,
                value: controller.isSoundAthkarNotificationOn.value,
                onChanged: (value) async {
                  await controller.changeSoundAthkarNotificationValue(value);
                },
                permissionController: permissionController,
                showLoadingOnChange: true, // Enable loading indicator
              ),
            ),
            8.horizontalSpace,
            TimeRangePicker(),
          ],
        ),
        // Show time range info when enabled
        if (controller.isSoundAthkarNotificationOn.value) ...[
          8.verticalSpace,
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: AppColor.kGreenColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                color: AppColor.kGreenColor.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColor.kGreenColor,
                  size: 16.sp,
                ),
                8.horizontalSpace,
                Expanded(
                  child: CustomText(
                    'Audio reminders will play different dhikr sounds at regular intervals during your selected time range.'
                        .tr,
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: AppColor.kGreenColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMorningAthkarSwitch(
    SettingsController controller,
    NotificationPermissionController permissionController,
  ) {
    // Get localized time string
    String localizedTime = controller.morningAthkarTime.isNotEmpty
        ? NumberLocalization.localizeTimeString(controller.morningAthkarTime)
        : NumberLocalization.localizeTimeString("7:30");

    return Row(
      children: [
        Expanded(
          child: buildPermissionBasedSwitch(
            title: 'Morning athkar',
            subtitle: controller.morningAthkarTime.isNotEmpty
                ? '${'You will be notified at'.tr} $localizedTime'.tr
                : '${'You will be notified at'.tr} $localizedTime'.tr,
            value: controller.isMorningNotificationOn.value,
            onChanged: (value) async {
              await controller.changeMorningAthkarNotificationValue(value);
            },
            permissionController: permissionController,
          ),
        ),
        8.horizontalSpace,
        IconButton.filled(
          icon: const Icon(Icons.settings),
          onPressed: () async {
            // Show custom Cupertino time picker
            await _showCupertinoTimePicker(
              context: Get.context!,
              initialTime: TimeOfDay(
                hour:
                    int.tryParse(controller.morningAthkarTime.split(':')[0]) ??
                        7,
                minute:
                    int.tryParse(controller.morningAthkarTime.split(':')[1]) ??
                        30,
              ),
              onTimeSelected: (selectedTime) async {
                await controller.setMorningAthkarTime(
                  '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}',
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildEveningAthkarSwitch(
    SettingsController controller,
    NotificationPermissionController permissionController,
  ) {
    // Get localized time string
    String localizedTime = controller.eveningAthkarTime.isNotEmpty
        ? NumberLocalization.localizeTimeString(controller.eveningAthkarTime)
        : NumberLocalization.localizeTimeString("13:15");

    return Row(
      children: [
        Expanded(
          child: buildPermissionBasedSwitch(
            title: 'Evening athkar',
            subtitle: controller.eveningAthkarTime.isNotEmpty
                ? '${'You will be notified at'.tr} $localizedTime'.tr
                : '${'You will be notified at'.tr} $localizedTime'.tr,
            value: controller.isEveningAthkarNotificationOn.value,
            onChanged: (value) async {
              await controller.changeEveningAthkarNotificationValue(value);
            },
            permissionController: permissionController,
          ),
        ),
        8.horizontalSpace,
        IconButton.filled(
          icon: const Icon(Icons.settings),
          onPressed: () async {
            // Show custom Cupertino time picker
            await _showCupertinoTimePicker(
              context: Get.context!,
              initialTime: TimeOfDay(
                hour:
                    int.tryParse(controller.eveningAthkarTime.split(':')[0]) ??
                        13,
                minute:
                    int.tryParse(controller.eveningAthkarTime.split(':')[1]) ??
                        15,
              ),
              onTimeSelected: (selectedTime) async {
                await controller.setEveningAthkarTime(
                  '${selectedTime.hour.toString().padLeft(2, '0')}:${selectedTime.minute.toString().padLeft(2, '0')}',
                );
              },
            );
          },
        ),
      ],
    );
  }

  // Widget _buildHasibNafisSwitch(
  //   SettingsController controller,
  //   NotificationPermissionController permissionController,
  // ) {
  //   return buildPermissionBasedSwitch(
  //     title: 'Hasib nafis',
  //     subtitle: '${'You will be notified at'.tr} 22:00'.tr,
  //     value: controller.isHasbAlNafsNotificationOn.value,
  //     onChanged: (value) async {
  //       await controller.changeHasbAlNafsNotificationValue(value);
  //     },
  //     permissionController: permissionController,
  //   );
  // }

  Future<void> showNotificationPermissionPopup(
    NotificationPermissionController permissionController,
  ) async {
    await Get.dialog(
      AlertDialog(
        backgroundColor: AppColor.kScaffoldColor,
        title: CustomText('enable_notifications'),
        content: CustomText('notification_settings_request'),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(); // Close the dialog
            },
            child: CustomText('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Get.back(); // Close the dialog
              await permissionController.checkAndRequestPermission();
              if (!permissionController.hasPermission) {
                // If permission is still not granted, show a message
                await AppSettings.openAppSettings(
                    type: AppSettingsType.notification);
              }
            },
            child: CustomText('allowNow'),
          ),
        ],
      ),
    );
  }

  Widget buildPermissionBasedSwitch({
    required String title,
    required String subtitle,
    required bool value,
    required Future<void> Function(bool) onChanged,
    required NotificationPermissionController permissionController,
    bool showLoadingOnChange = false,
  }) {
    return Obx(() {
      final hasPermission = permissionController.hasPermission;
      return NotificationSwitchItem(
        title: title.tr,
        subtitle: subtitle.tr,
        value: hasPermission ? value : false,
        onChanged: (newValue) async {
          if (!hasPermission) {
            // Show a popup to request permission
            await showNotificationPermissionPopup(permissionController);
            if (!permissionController.hasPermission) {
              // If permission is still not granted, return
              return;
            }
          }
          // Call the original onChanged callback
          await onChanged(newValue);
        },
        isEnabled: hasPermission,
        showLoadingOnChange: showLoadingOnChange,
        onPermissionRequested: () async {
          // Show a popup to request permission
          await showNotificationPermissionPopup(permissionController);
        },
      );
    });
  }

  Future<void> _showCupertinoTimePicker({
    required BuildContext context,
    required TimeOfDay initialTime,
    required Function(TimeOfDay) onTimeSelected,
  }) async {
    TimeOfDay selectedTime = initialTime;
    final String languageCode = NumberLocalization.getDeviceLocale();
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: AppColor.kScaffoldColor,
          title: CustomText('Choose Start Time'),
          content: SizedBox(
            height: 200,
            child: Localizations.override(
              context: context,
              locale: Locale(languageCode),
              delegates: const [
                DefaultCupertinoLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
              ],
              child: CupertinoTheme(
                data: CupertinoThemeData(
                  textTheme: CupertinoTextThemeData(
                    dateTimePickerTextStyle: TextStyle(
                      color: Colors.white,
                      // fontSize: 16.sp,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                ),
                child: CupertinoDatePicker(
                  mode: CupertinoDatePickerMode.time,
                  initialDateTime: DateTime(
                    DateTime.now().year,
                    DateTime.now().month,
                    DateTime.now().day,
                    initialTime.hour,
                    initialTime.minute,
                  ),
                  onDateTimeChanged: (DateTime newDateTime) {
                    selectedTime = TimeOfDay(
                      hour: newDateTime.hour,
                      minute: newDateTime.minute,
                    );
                  },
                  use24hFormat: NumberLocalization.getDeviceLocale() != 'en',
                  backgroundColor: AppColor.kScaffoldColor,
                ),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: CustomText('Cancel'),
            ),
            TextButton(
              onPressed: () {
                onTimeSelected(selectedTime);
                Navigator.pop(context);
              },
              child: CustomText('ok'),
            ),
          ],
        );
      },
    );
  }
}

class NotificationSwitchItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final bool value;
  final Future<void> Function(bool) onChanged;
  final bool isEnabled;
  final bool showLoadingOnChange;
  final VoidCallback onPermissionRequested;

  const NotificationSwitchItem({
    super.key,
    required this.title,
    required this.subtitle,
    required this.value,
    required this.onChanged,
    required this.isEnabled,
    required this.onPermissionRequested,
    this.showLoadingOnChange = false,
  });

  @override
  Widget build(BuildContext context) {
    return SettingsSwitchItemBuilder(
      title: title,
      subtitle: subtitle,
      switchValue: value,
      onChanged: isEnabled ? onChanged : null,
      isEnabled: isEnabled,
      showLoadingOnChange: showLoadingOnChange,
      onPermissionRequested: onPermissionRequested,
    );
  }
}

class NotificationPermissionController extends GetxController {
  final RxBool _hasPermission = false.obs;
  bool get hasPermission => _hasPermission.value;

  Future<void> checkAndRequestPermission() async {
    final result = await AwesomeNotificationsService.requestPermissions();
    _hasPermission.value = result;
  }
}

extension SettingsNotificationScreenExtension on SettingsNotificationScreen {
  Widget _buildTestDhikrButton(SettingsController controller) {
    return SmoothEdgesContainer(
      borderRadius: BorderRadius.circular(60.r),
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
      color: AppColor.kRectangleColor,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      'Test Audio Athkar'.tr,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    4.verticalSpace,
                    CustomText(
                      'Test all dhikr notifications with sound - one every minute starting in 1 minute.'
                          .tr,
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: AppColor.kWhiteColor.withOpacity(0.6),
                      ),
                    ),
                  ],
                ),
              ),
              8.horizontalSpace,
              ElevatedButton(
                onPressed: () async {
                  try {
                    debugPrint(
                        '🧪 Manual test: Scheduling all dhikr notifications in 1 minute');

                    // Get all dhikr items
                    final dhikrItems = PredefinedDhikr.getEnabledItems();
                    debugPrint(
                        '🧪 Found ${dhikrItems.length} dhikr items to test');

                    // Schedule each dhikr item with 1-minute intervals starting from now
                    final now = DateTime.now();
                    int scheduledCount = 0;

                    for (int i = 0; i < dhikrItems.length; i++) {
                      final dhikrItem = dhikrItems[i];
                      final scheduledTime = now.add(Duration(
                          minutes:
                              i + 1)); // Start from 1 minute, then 2, 3, etc.

                      debugPrint(
                          '🧪 Scheduling dhikr ${i + 1}/${dhikrItems.length}: ${dhikrItem.title}');
                      debugPrint('🧪 Sound file: ${dhikrItem.soundFile}');
                      debugPrint(
                          '🧪 Scheduled for: ${scheduledTime.toString()}');

                      // Create notification data
                      final notificationData = AthkarNotificationData(
                        id: 90000 + i, // Test IDs starting from 90000
                        title: 'Test: ${dhikrItem.title}',
                        body: dhikrItem.subtitle,
                        scheduledTime: scheduledTime,
                        channelKey: 'athkar_channel',
                        notificationType: 'dhikr_reminder',
                        athkarType: AthkarType.dhikr,
                        dhikrItem: dhikrItem,
                        payload: 'sound_notifications',
                        soundPath:
                            'resource://raw/${dhikrItem.soundFile.replaceAll('.mp3', '')}',
                      );

                      debugPrint(
                          '🧪 Sound path: ${notificationData.soundPath}');

                      // Schedule the notification
                      final success = await AwesomeNotificationsService
                          .scheduleTestNotification(
                        title: notificationData.title,
                        body: notificationData.body,
                        scheduledTime: notificationData.scheduledTime,
                        soundName: dhikrItem.soundFile.replaceAll('.mp3', ''),
                      );

                      if (success) {
                        scheduledCount++;
                        debugPrint(
                            '🧪 ✅ Successfully scheduled dhikr ${i + 1}');
                      } else {
                        debugPrint('🧪 ❌ Failed to schedule dhikr ${i + 1}');
                      }
                    }

                    Get.snackbar(
                      'Test Complete',
                      'Scheduled $scheduledCount/${dhikrItems.length} dhikr notifications! First one in 1 minute.',
                      duration: const Duration(seconds: 5),
                      backgroundColor: AppColor.kGreenColor,
                      colorText: Colors.white,
                    );

                    debugPrint(
                        '🧪 Test complete: $scheduledCount notifications scheduled');
                  } catch (e) {
                    Get.snackbar(
                      'Test Failed',
                      'Error: $e',
                      duration: const Duration(seconds: 3),
                      backgroundColor: Colors.red,
                      colorText: Colors.white,
                    );
                    debugPrint('🧪 Test error: $e');
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.kGreenColor,
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                ),
                child: CustomText(
                  'Test All',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
