import 'package:awesome_notifications_service/awesome_notifications_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/services/notification_service_manager.dart';
import '../../../../core/utils/app_color.dart';
import '../../../../core/utils/custom_text.dart';
import '../../../athkar/presentation/screens/athkar_screen.dart';
import '../../../layout/presentation/controller/layout_controller.dart';
import '../../../prayer/presentation/controller/prayer_controller.dart';
import 'production_deployment_screen.dart';

/// Developer settings screen for testing notification systems
class DeveloperSettingsScreen extends StatefulWidget {
  const DeveloperSettingsScreen({super.key});

  @override
  State<DeveloperSettingsScreen> createState() =>
      _DeveloperSettingsScreenState();
}

class _DeveloperSettingsScreenState extends State<DeveloperSettingsScreen> {
  bool _isLoading = false;
  String _statusMessage = '';
  int _scheduledNotificationsCount = 0;

  @override
  void initState() {
    super.initState();
    _loadCurrentSettings();
  }

  void _loadCurrentSettings() {
    setState(() {
      _statusMessage =
          'Current system: ${NotificationServiceManager.currentSystemName}';
    });
    _updateNotificationCount();
  }

  Future<void> _updateNotificationCount() async {
    try {
      final count = await AwesomeNotificationsService
          .getTotalScheduledNotificationsCount();
      setState(() {
        _scheduledNotificationsCount = count;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error getting notification count: $e');
      }
    }
  }

  Future<void> _rescheduleNotifications() async {
    try {
      setState(() {
        _statusMessage = 'Rescheduling notifications...';
      });

      final prayerController = Get.find<PrayerController>();
      await prayerController.athanNotificationSetup();

      setState(() {
        _statusMessage = 'Notifications rescheduled successfully';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error rescheduling notifications: $e';
      });
    }
  }

  Future<void> _testNotificationPermissions() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Testing permissions...';
    });

    try {
      final hasPermissions =
          await NotificationServiceManager.requestPermissions();
      setState(() {
        _statusMessage = hasPermissions
            ? 'All permissions granted'
            : 'Some permissions denied';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error testing permissions: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _cancelAllNotifications() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Cancelling all notifications...';
    });

    try {
      await NotificationServiceManager.cancelAllNotifications();
      await _updateNotificationCount();
      setState(() {
        _statusMessage = 'All notifications cancelled';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error cancelling notifications: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testNotificationIn5Seconds() async {
    setState(() {
      _statusMessage = 'Test notification will appear in 5 seconds...';
    });

    try {
      // Test with awesome_notifications
      await AwesomeNotificationsService.scheduleTestNotification(
        title: 'Test Notification',
        body: 'This is a test notification from awesome_notifications!',
        scheduledTime: DateTime.now().add(const Duration(seconds: 5)),
      );

      setState(() {
        _statusMessage = 'Test notification scheduled for 5 seconds from now';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error scheduling test notification: $e';
      });
    }
  }

  Future<void> _testAthkarNotification() async {
    setState(() {
      _statusMessage = 'Testing athkar notification...';
    });

    try {
      // Test awesome athkar notification
      final config = AthkarNotificationConfig(
        type: AthkarType.morning,
        time:
            '${DateTime.now().add(Duration(seconds: 10)).hour.toString().padLeft(2, '0')}:${DateTime.now().add(Duration(seconds: 10)).minute.toString().padLeft(2, '0')}',
        isEnabled: true,
      );

      final success = await AwesomeNotificationsService.scheduleMorningAthkar(
        config: config,
        daysAhead: 1,
        locale: 'ar',
      );

      setState(() {
        _statusMessage = success
            ? 'Athkar notification scheduled for 10 seconds from now. Tap the notification to test navigation to athkar screen.'
            : 'Failed to schedule athkar notification';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error testing athkar notification: $e';
      });
    }
  }

  Future<void> _testDhikrNotifications() async {
    setState(() {
      _statusMessage = 'Testing dhikr notifications...';
    });

    try {
      // Test dhikr reminders with all predefined dhikr items
      final config = AthkarNotificationConfig(
        type: AthkarType.dhikr,
        time:
            '${DateTime.now().hour.toString().padLeft(2, '0')}:${DateTime.now().minute.toString().padLeft(2, '0')}-${DateTime.now().add(Duration(hours: 2)).hour.toString().padLeft(2, '0')}:${DateTime.now().add(Duration(hours: 2)).minute.toString().padLeft(2, '0')}',
        isEnabled: true,
        intervalMinutes: 5, // Every 5 minutes for testing
        dhikrItems: PredefinedDhikr.getEnabledItems(),
      );

      debugPrint('🧪 Testing dhikr config: ${config.toString()}');
      debugPrint('🧪 Dhikr items count: ${config.dhikrItems.length}');

      final success = await AwesomeNotificationsService.scheduleDhikrReminders(
        config: config,
        daysAhead: 1,
        locale: 'ar',
      );

      setState(() {
        _statusMessage = success
            ? 'Dhikr notifications scheduled! Check logs for details. First notification should appear within 5 minutes.'
            : 'Failed to schedule dhikr notifications';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error testing dhikr notifications: $e';
      });
      debugPrint('🧪 Dhikr test error: $e');
    }
  }

  Future<void> _testSingleDhikrNotification() async {
    setState(() {
      _statusMessage = 'Testing single dhikr notification in 10 seconds...';
    });

    try {
      // Get first dhikr item for testing
      final dhikrItems = PredefinedDhikr.getEnabledItems();
      if (dhikrItems.isEmpty) {
        setState(() {
          _statusMessage = 'No dhikr items available for testing';
        });
        return;
      }

      final testDhikr = dhikrItems.first;
      debugPrint('🧪 Testing with dhikr: ${testDhikr.title}');
      debugPrint('🧪 Sound file: ${testDhikr.soundFile}');

      // Schedule a single dhikr notification for 10 seconds from now
      final scheduledTime = DateTime.now().add(Duration(seconds: 10));

      final notificationData = AthkarNotificationData(
        id: 99999, // Test ID
        title: testDhikr.title,
        body: testDhikr.subtitle,
        scheduledTime: scheduledTime,
        channelKey: 'athkar_channel',
        notificationType: 'dhikr_reminder',
        athkarType: AthkarType.dhikr,
        dhikrItem: testDhikr,
        payload: 'sound_notifications',
        soundPath:
            'resource://raw/${testDhikr.soundFile.replaceAll('.mp3', '')}',
      );

      debugPrint(
          '🧪 Scheduling test dhikr notification with sound: ${notificationData.soundPath}');

      final success =
          await AwesomeNotificationsService.scheduleTestNotification(
        title: notificationData.title,
        body: notificationData.body,
        scheduledTime: notificationData.scheduledTime,
        soundName: testDhikr.soundFile.replaceAll('.mp3', ''),
      );

      setState(() {
        _statusMessage = success
            ? 'Single dhikr notification scheduled for 10 seconds from now with sound: ${testDhikr.soundFile}'
            : 'Failed to schedule single dhikr notification';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error testing single dhikr notification: $e';
      });
      debugPrint('🧪 Single dhikr test error: $e');
    }
  }

  Future<void> _testPrayerNotification() async {
    setState(() {
      _statusMessage = 'Testing prayer notification...';
    });

    try {
      // Test awesome prayer notification
      final success =
          await AwesomeNotificationsService.scheduleTestPrayerNotification(
        prayerName: 'Fajr',
        scheduledTime: DateTime.now().add(const Duration(seconds: 5)),
        locale: 'ar',
      );

      setState(() {
        _statusMessage = success
            ? 'Prayer notification scheduled for 5 seconds from now'
            : 'Failed to schedule prayer notification';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error testing prayer notification: $e';
      });
    }
  }

  Future<void> _testAthkarNavigation() async {
    setState(() {
      _statusMessage = 'Testing athkar navigation...';
    });

    try {
      // Test navigation to athkar screen using layout controller
      final layoutController = Get.find<LayoutController>();
      layoutController.changeScreenLayout(const AthkarScreen());

      setState(() {
        _statusMessage =
            'Athkar navigation test completed. Check if athkar screen opened.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error testing athkar navigation: $e';
      });
    }
  }

  void _openProductionDeployment() {
    Get.to(() => const ProductionDeploymentScreen());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColor.kScaffoldColor,
      appBar: AppBar(
        title: CustomText(
          'Developer Settings',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColor.kScaffoldColor,
        elevation: 0,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Information
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      'Status',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    CustomText(
                      _statusMessage,
                      style: TextStyle(
                        fontSize: 14.sp,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    CustomText(
                      'Scheduled notifications: $_scheduledNotificationsCount',
                      style: TextStyle(
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16.h),

            // Action Buttons
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                      'Actions',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16.h),

                    // Test Permissions Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed:
                            _isLoading ? null : _testNotificationPermissions,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColor.kOrangeColor,
                        ),
                        child: CustomText(
                          'Test Permissions',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 8.h),

                    // Reschedule Notifications Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _rescheduleNotifications,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                        ),
                        child: CustomText(
                          'Reschedule Notifications',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 8.h),

                    // Cancel All Notifications Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _cancelAllNotifications,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                        ),
                        child: CustomText(
                          'Cancel All Notifications',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 8.h),

                    // Test Notification in 5 Seconds Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed:
                            _isLoading ? null : _testNotificationIn5Seconds,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                        ),
                        child: CustomText(
                          'Test Notification (5 seconds)',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 8.h),

                    // Test Athkar Notification Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testAthkarNotification,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                        ),
                        child: CustomText(
                          'Test Athkar Notification (10 seconds)',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 8.h),

                    // Test Athkar Navigation Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testAthkarNavigation,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.indigo,
                        ),
                        child: CustomText(
                          'Test Athkar Navigation',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 8.h),

                    // Test Dhikr Notifications Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testDhikrNotifications,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                        ),
                        child: CustomText(
                          'Test Dhikr Notifications (5 min intervals)',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 8.h),

                    // Test Single Dhikr Notification Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed:
                            _isLoading ? null : _testSingleDhikrNotification,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepOrange,
                        ),
                        child: CustomText(
                          'Test Single Dhikr (10 seconds)',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 8.h),

                    // Test Prayer Notification Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _testPrayerNotification,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.teal,
                        ),
                        child: CustomText(
                          'Test Prayer Notification (5 seconds)',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 16.h),

                    // Production Deployment Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _openProductionDeployment,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.deepPurple,
                        ),
                        child: CustomText(
                          'Production Deployment Control',
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            if (_isLoading) ...[
              SizedBox(height: 16.h),
              const Center(
                child: CircularProgressIndicator(),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
